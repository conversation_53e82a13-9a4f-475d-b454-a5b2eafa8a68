<%@ Page Title="开通VIP会员" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MemberUpgrade.aspx.cs" Inherits="Account.Web.MemberUpgrade" %>
<%@ Import Namespace="CommonLib" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%@ Import Namespace="System.Linq" %>

<script runat="server">
    public class ChargeViewToUser
    {
        public string Name { get; set; }
        public string Desc { get; set; }
        public double Price { get; set; }
        public double OriPrice { get; set; }
        public bool IsDefault { get; set; }
        public string Tag { get; set; }
    }
</script>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="开通OCR文字识别助手VIP会员，享受更多专业功能，个人版、专业版、旗舰版多种选择">
    <style type="text/css">
        .upgrade-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 20px;
        }
        
        .upgrade-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .upgrade-header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .upgrade-header p {
            font-size: 1.1rem;
            color: #666;
        }

        .user-info-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .user-info-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
        }

        .user-info-card p {
            margin: 5px 0;
            opacity: 0.9;
        }

        .plans-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .plan-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            border: 2px solid transparent;
        }

        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .plan-card.recommended {
            border-color: #007bff;
            transform: scale(1.05);
        }

        .plan-card.recommended::before {
            content: "推荐";
            position: absolute;
            top: -10px;
            right: 20px;
            background: #007bff;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .plan-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .plan-icon {
            width: 60px;
            height: 40px;
            margin: 0 auto 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .icon-v-1 {
            background-image: url(/static/image/vip_1.png);
        }

        .icon-v1 {
            background-image: url(/static/image/vip_2.png);
        }

        .icon-v3 {
            background-image: url(/static/image/vip_3.png);
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .plan-description {
            color: #666;
            font-size: 0.9rem;
        }

        .plan-features {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .plan-features li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .plan-features li:last-child {
            border-bottom: none;
        }

        .feature-name {
            font-weight: 500;
            color: #333;
        }

        .feature-value {
            color: #007bff;
            font-weight: bold;
        }

        .feature-check {
            color: #28a745;
            font-weight: bold;
        }

        .feature-cross {
            color: #dc3545;
            font-weight: bold;
        }

        .plan-pricing {
            text-align: center;
            margin: 20px 0;
            padding: 20px 0;
            border-top: 2px solid #f0f0f0;
        }

        .price-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .price-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .price-option:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .price-option.selected {
            border-color: #007bff;
            background-color: #e3f2fd;
        }

        .price-duration {
            font-weight: 500;
        }

        .price-amount {
            font-weight: bold;
            color: #007bff;
        }

        .price-discount {
            font-size: 0.8rem;
            color: #28a745;
        }

        .upgrade-btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .upgrade-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            text-decoration: none;
        }

        .btn-personal {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-professional {
            background: linear-gradient(135deg, #4B4B4B 0%, #1A1510 100%);
            color: #F9D9A8;
        }

        .btn-flagship {
            background: linear-gradient(135deg, #FFEBC1 0%, #FFE5B7 100%);
            color: #944800;
        }

        .comparison-link {
            text-align: center;
            margin-top: 30px;
        }

        .comparison-link a {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }

        .comparison-link a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .upgrade-container {
                margin: 20px auto;
                padding: 15px;
            }
            
            .upgrade-header h1 {
                font-size: 2rem;
            }
            
            .plans-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .plan-card {
                padding: 20px;
            }
            
            .plan-card.recommended {
                transform: none;
            }
            
            .user-info-card {
                padding: 15px;
            }
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="upgrade-container">
        <div class="upgrade-header">
            <h1>开通VIP会员</h1>
            <p>选择适合您的会员等级，解锁更多专业功能</p>
        </div>

        <%
            var strAccount = Request.QueryString["account"];
            UserType nextType = null;
            if (string.IsNullOrEmpty(strAccount))
            {
                try
                {
                    var tmpAcc = Account.Web.Common.AuthHelper.GetUserSession(Request);
                    if (tmpAcc != null)
                        strAccount = tmpAcc.Account;
                }
                catch { }
            }
        %>

        <% if (!string.IsNullOrEmpty(strAccount)) { %>
        <div class="user-info-card">
            <h3>当前账号信息</h3>
            <p><strong>账号：</strong><%=strAccount %></p>
            <%
                var currentUser = Account.Web.CodeHelper.GetCodeByAccountId(strAccount);
                if (currentUser != null) {
            %>
            <p><strong>当前版本：</strong><%=currentUser.StrType %></p>
            <p><strong>到期时间：</strong><%=currentUser.DtExpire.Year > 1900 ? currentUser.DtExpire.ToString("yyyy-MM-dd") : "永久" %></p>
            <% } %>
        </div>
        <% } %>

        <div class="plans-container">
            <%
                var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();
                var selectedType = Request.QueryString["type"];

                foreach (var userType in lstUserTypes)
                {
                    var isRecommended = userType.Type.ToString() == "专业版";
                    var typeHash = userType.Type.GetHashCode().ToString();
                    var isSelected = selectedType == typeHash;
            %>
            <div class="plan-card <%=isRecommended ? "recommended" : "" %>">
                <div class="plan-header">
                    <div class="plan-icon icon-v<%=typeHash %>"></div>
                    <div class="plan-name"><%=userType.Type.ToString() %></div>
                    <div class="plan-description">
                        <%
                            var typeName = userType.Type.ToString();
                            if (typeName == "个人版") {
                        %>
                        个人专属，轻松识别
                        <%
                            } else if (typeName == "专业版") {
                        %>
                        专业高效，精确稳定
                        <%
                            } else if (typeName == "旗舰版") {
                        %>
                        旗舰体验，最佳选择
                        <%
                            }
                        %>
                    </div>
                </div>

                <ul class="plan-features">
                    <li>
                        <span class="feature-name">批量识别</span>
                        <span class="<%=userType.IsSupportBatch ? "feature-check" : "feature-cross" %>">
                            <%=userType.IsSupportBatch ? "✔" : "✘" %>
                        </span>
                    </li>
                    <li>
                        <span class="feature-name">表格识别</span>
                        <span class="<%=userType.IsSupportTable ? "feature-check" : "feature-cross" %>">
                            <%=userType.IsSupportTable ? "✔" : "✘" %>
                        </span>
                    </li>
                    <li>
                        <span class="feature-name">公式识别</span>
                        <span class="<%=userType.IsSupportMath ? "feature-check" : "feature-cross" %>">
                            <%=userType.IsSupportMath ? "✔" : "✘" %>
                        </span>
                    </li>
                    <li>
                        <span class="feature-name">文档识别</span>
                        <span class="<%=userType.IsSupportDocFile ? "feature-check" : "feature-cross" %>">
                            <%=userType.IsSupportDocFile ? "✔" : "✘" %>
                        </span>
                    </li>
                    <li>
                        <span class="feature-name">翻译功能</span>
                        <span class="<%=userType.IsSupportTranslate ? "feature-check" : "feature-cross" %>">
                            <%=userType.IsSupportTranslate ? "✔" : "✘" %>
                        </span>
                    </li>
                    <li>
                        <span class="feature-name">每日识别次数</span>
                        <span class="feature-value">
                            <%=userType.LimitPerDayCount <= 0 ? "不限制" : userType.LimitPerDayCount + "次" %>
                        </span>
                    </li>
                    <li>
                        <span class="feature-name">设备数量</span>
                        <span class="feature-value">
                            <%=userType.MaxLoginCount <= 1 ? "1台" : userType.MaxLoginCount + "台" %>
                        </span>
                    </li>
                </ul>

                <div class="plan-pricing">
                    <div class="price-options" data-type="<%=typeHash %>">
                        <%
                            // 构建价格选项列表（参考UserUpgrade.aspx的逻辑）
                            var lstChargeType = new List<ChargeViewToUser>();
                            try
                            {
                                if (userType.ChargeTypes != null)
                                {
                                    foreach (var q in userType.ChargeTypes)
                                    {
                                        string strDesc = "";
                                        var price = q.GetPrice(userType.PerPrice, ref strDesc);
                                        var charge = new ChargeViewToUser()
                                        {
                                            Name = q.Name,
                                            Desc = strDesc,
                                            Price = (double)price,
                                            OriPrice = (double)q.OriPrice,
                                            IsDefault = q.IsDefault,
                                            Tag = q.Tag,
                                        };
                                        lstChargeType.Add(charge);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                // 如果价格计算失败，使用默认价格
                                lstChargeType.Add(new ChargeViewToUser
                                {
                                    Name = "一年",
                                    Desc = "",
                                    Price = 100,
                                    OriPrice = 100,
                                    IsDefault = true,
                                    Tag = ""
                                });
                            }

                            for (int i = 0; i < lstChargeType.Count; i++)
                            {
                                var payType = lstChargeType[i];
                                var isFirstOption = payType.IsDefault || i == 0;
                        %>
                        <div class="price-option <%=isFirstOption ? "selected" : "" %>"
                             data-months="<%=payType.Name %>"
                             data-price="<%=payType.Price %>"
                             data-remark="<%=payType.Name %>">
                            <div>
                                <div class="price-duration"><%=payType.Name %></div>
                                <%if (!string.IsNullOrEmpty(payType.Desc)) { %>
                                <div class="price-discount"><%=payType.Desc %></div>
                                <%} %>
                            </div>
                            <div class="price-amount">¥<%=payType.Price %></div>
                        </div>
                        <%
                            }
                        %>
                    </div>
                </div>

                <a href="javascript:void(0)"
                   class="upgrade-btn btn-<%=userType.Type.ToString() == "个人版" ? "personal" : (userType.Type.ToString() == "专业版" ? "professional" : "flagship") %>"
                   onclick="upgradeToType('<%=typeHash %>', '<%=userType.Type.ToString() %>')">
                    立即升级到<%=userType.Type.ToString() %>
                </a>
            </div>
            <%
                }
            %>
        </div>

        <div class="comparison-link">
            <a href="Version.aspx">查看详细功能对比 →</a>
        </div>
    </div>

    <script type="text/javascript">
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initPriceOptions();
        });

        // 初始化价格选项
        function initPriceOptions() {
            var priceContainers = document.querySelectorAll('.price-options');

            priceContainers.forEach(function(container) {
                var options = container.querySelectorAll('.price-option');

                options.forEach(function(option) {
                    option.addEventListener('click', function() {
                        // 移除同组其他选项的选中状态
                        options.forEach(function(opt) {
                            opt.classList.remove('selected');
                        });

                        // 添加当前选项的选中状态
                        this.classList.add('selected');
                    });
                });
            });
        }

        // 升级到指定类型
        function upgradeToType(typeHash, typeName) {
            var container = document.querySelector('.price-options[data-type="' + typeHash + '"]');
            if (!container) {
                alert('请选择升级方案');
                return;
            }

            var selectedOption = container.querySelector('.price-option.selected');
            if (!selectedOption) {
                alert('请选择升级时长');
                return;
            }

            var months = selectedOption.getAttribute('data-months');
            var price = selectedOption.getAttribute('data-price');
            var remark = selectedOption.getAttribute('data-remark');

            // 确认升级
            var confirmMsg = '确认升级到' + typeName + '（' + remark + '）？\n价格：¥' + price;
            if (!confirm(confirmMsg)) {
                return;
            }

            // 跳转到支付页面
            var payUrl = 'UserUpgrade.aspx?type=' + typeHash + '&months=' + months + '&price=' + price;
            <%if (!string.IsNullOrEmpty(strAccount)) { %>
            payUrl += '&account=<%=strAccount %>';
            <%} %>

            window.location.href = payUrl;
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            // 移动端自动选择推荐方案
            if (window.innerWidth <= 768) {
                var recommendedCard = document.querySelector('.plan-card.recommended');
                if (recommendedCard) {
                    recommendedCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });

        // 页面加载时的特殊处理
        window.addEventListener('load', function() {
            // 如果URL中指定了type参数，自动滚动到对应的卡片
            var urlParams = new URLSearchParams(window.location.search);
            var selectedType = urlParams.get('type');

            if (selectedType) {
                var targetCard = document.querySelector('.price-options[data-type="' + selectedType + '"]');
                if (targetCard) {
                    var card = targetCard.closest('.plan-card');
                    if (card) {
                        card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        // 添加高亮效果
                        card.style.boxShadow = '0 0 20px rgba(0, 123, 255, 0.5)';
                        setTimeout(function() {
                            card.style.boxShadow = '';
                        }, 3000);
                    }
                }
            }
        });
    </script>
</asp:Content>

﻿<%@ Page Title="开通VIP会员" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="MemberUpgrade.aspx.cs" Inherits="Account.Web.MemberUpgrade" %>
<%@ Import Namespace="CommonLib" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%@ Import Namespace="System.Linq" %>

<script runat="server">
    public class ChargeViewToUser
    {
        public string Name { get; set; }
        public string Desc { get; set; }
        public double Price { get; set; }
        public double OriPrice { get; set; }
        public bool IsDefault { get; set; }
        public string Tag { get; set; }
    }
</script>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <meta name="description" content="开通OCR文字识别助手VIP会员，享受更多专业功能，个人版、专业版、旗舰版多种选择">
    <style type="text/css">
        .upgrade-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 20px;
        }

        .upgrade-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .upgrade-header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .upgrade-header p {
            font-size: 1.1rem;
            color: #666;
        }

        .user-info-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .user-info-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
        }

        .user-info-card p {
            margin: 5px 0;
            font-size: 1rem;
        }

        .plans-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .plan-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            border: 2px solid #e9ecef;
            cursor: pointer;
        }

        .plan-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            border-color: #dee2e6;
        }

        .plan-card.plan-personal {
            background: linear-gradient(89.95deg, #6666FF 11.5%, #38c0ff 100.01%);
            color: white;
            border-color: #6666FF;
        }

        .plan-card.plan-personal .plan-name,
        .plan-card.plan-personal .plan-description,
        .plan-card.plan-personal .feature-item {
            color: white;
        }

        .plan-card.plan-personal .feature-icon {
            color: #ffffff;
        }

        .plan-card.plan-professional {
            background: linear-gradient(to right, #4B4B4B 5.77%, #1A1510 100%);
            color: #FFE4A3;
            border-color: #4B4B4B;
            transform: scale(1.05);
        }

        .plan-card.plan-professional .plan-name,
        .plan-card.plan-professional .plan-description,
        .plan-card.plan-professional .feature-item {
            color: #FFE4A3;
        }

        .plan-card.plan-professional .feature-icon {
            color: #FFE4A3;
        }

        .plan-card.plan-flagship {
            background: linear-gradient(to right, #FFEBC1 21.65%, #FFE5B7 79.13%);
            color: #8B4000;
            border-color: #FFE5B7;
        }

        .plan-card.plan-flagship .plan-name,
        .plan-card.plan-flagship .plan-description,
        .plan-card.plan-flagship .feature-item {
            color: #8B4000;
        }

        .plan-card.plan-flagship .feature-icon {
            color: #8B4000;
        }

        /* 价格文字颜色优化 */
        .plan-card.plan-personal .price-option .original-price {
            color: rgba(255, 255, 255, 0.6);
        }

        .plan-card.plan-professional .price-option .original-price {
            color: rgba(255, 228, 163, 0.6);
        }

        .plan-card.plan-flagship .price-option .original-price {
            color: rgba(139, 64, 0, 0.6);
        }

        .plan-card.recommended::after {
            content: "推荐";
            position: absolute;
            top: -8px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 6px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }



        .plan-card {
            transform-style: preserve-3d;
        }

        .plan-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .plan-icon {
            width: 60px;
            height: 40px;
            margin: 0 auto 15px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .icon-v-1 {
            background-image: url(/static/image/vip_1.png);
        }

        .icon-v1 {
            background-image: url(/static/image/vip_2.png);
        }

        .icon-v3 {
            background-image: url(/static/image/vip_3.png);
        }

        .plan-name {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .plan-description {
            color: #666;
            font-size: 1rem;
        }

        .plan-features {
            margin: 25px 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 0.95rem;
        }

        .feature-icon {
            color: #28a745;
            margin-right: 10px;
            font-weight: bold;
        }

        .plan-pricing {
            margin: 25px 0;
        }

        .price-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .price-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border: 2px solid rgba(255, 255, 255, 0.4);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
        }

        .price-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 12px;
            padding: 2px;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.5), transparent);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .price-option:hover {
            transform: translateY(-2px);
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .price-option:hover::before {
            opacity: 1;
        }

        .price-option.selected {
            border-color: #ffd700;
            background: rgba(255, 215, 0, 0.2);
            transform: scale(1.02);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        }

        .price-option.selected::after {
            content: '✓';
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            background: #ffd700;
            color: #333;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(255, 215, 0, 0.5);
        }

        .price-option .duration {
            font-weight: bold;
            color: inherit;
        }

        .price-option .price {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .price-option .original-price {
            text-decoration: line-through;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }

        .price-option .current-price {
            color: inherit;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .price-option .discount {
            background: #ff4757;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .upgrade-btn {
            width: 100%;
            padding: 14px 24px;
            border: 2px solid transparent;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: block;
            position: relative;
        }

        .btn-personal {
            background: rgba(255, 255, 255, 0.25);
            color: white;
            border-color: rgba(255, 255, 255, 0.5);
            font-weight: 700;
        }

        .btn-professional {
            background: rgba(255, 228, 163, 0.3);
            color: #FFE4A3;
            border-color: rgba(255, 228, 163, 0.6);
            font-weight: 700;
        }

        .btn-flagship {
            background: rgba(139, 64, 0, 0.25);
            color: #8B4000;
            border-color: rgba(139, 64, 0, 0.5);
            font-weight: 700;
        }

        .upgrade-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            opacity: 0.9;
        }

        .upgrade-btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        .comparison-link {
            text-align: center;
            margin-top: 40px;
        }

        .comparison-link a {
            color: #007bff;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .comparison-link a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .upgrade-container {
                padding: 15px;
            }

            .plans-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .plan-card.recommended {
                transform: none;
            }

            .upgrade-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="upgrade-container">
        <div class="upgrade-header">
            <h1>开通VIP会员</h1>
            <p>选择适合您的会员等级，解锁更多专业功能</p>
        </div>

        <%
            var strAccount = Request.QueryString["account"];
            UserType nextType = null;
            if (string.IsNullOrEmpty(strAccount))
            {
                try
                {
                    var tmpAcc = Account.Web.Common.AuthHelper.GetUserSession(Request);
                    if (tmpAcc != null)
                        strAccount = tmpAcc.Account;
                }
                catch { }
            }
        %>

        <% if (!string.IsNullOrEmpty(strAccount)) { %>
        <div class="user-info-card">
            <h3>当前账号信息</h3>
            <p><strong>账号：</strong><%=strAccount %></p>
            <%
                var currentUser = Account.Web.CodeHelper.GetCodeByAccountId(strAccount);
                if (currentUser != null) {
            %>
            <p><strong>当前版本：</strong><%=currentUser.StrType %></p>
            <p><strong>到期时间：</strong><%=currentUser.DtExpire.Year > 1900 ? currentUser.DtExpire.ToString("yyyy-MM-dd") : "永久" %></p>
            <% } %>
        </div>
        <% } %>

        <div class="plans-container">
            <%
                var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();
                var selectedType = Request.QueryString["type"];

                foreach (var userType in lstUserTypes)
                {
                    var isRecommended = userType.Type.ToString() == "专业版";
                    var typeHash = userType.Type.GetHashCode().ToString();
                    var isSelected = selectedType == typeHash;
                    var typeName = userType.Type.ToString();
            %>
            <%
                var planClass = "plan-card";
                if (typeName == "个人版") planClass += " plan-personal";
                else if (typeName == "专业版") planClass += " plan-professional";
                else if (typeName == "旗舰版") planClass += " plan-flagship";
                if (isRecommended) planClass += " recommended";
            %>
            <div class="<%=planClass %>" data-plan="<%=typeName %>">
                <div class="plan-header">
                    <div class="plan-icon icon-v<%=typeHash %>"></div>
                    <div class="plan-name"><%=userType.Type.ToString() %></div>
                    <div class="plan-description">
                        <%
                            if (typeName == "个人版") {
                        %>
                        个人专属，轻松识别
                        <%
                            } else if (typeName == "专业版") {
                        %>
                        专业高效，精确稳定
                        <%
                            } else if (typeName == "旗舰版") {
                        %>
                        旗舰体验，最佳选择
                        <%
                            }
                        %>
                    </div>
                </div>

                <div class="plan-features">
                    <%if (userType.IsSupportLocalOcr) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        离线识别
                    </div>
                    <%} %>
                    <%if (userType.IsSupportImageFile) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        图片识别
                    </div>
                    <%} %>
                    <%if (userType.IsSupportVertical) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        竖排识别
                    </div>
                    <%} %>
                    <%if (userType.IsSupportTranslate) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        划词翻译
                    </div>
                    <%} %>
                    <%if (userType.IsSupportMath) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        公式识别
                    </div>
                    <%} %>
                    <%if (userType.IsSupportTable) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        表格识别
                    </div>
                    <%} %>
                    <%if (userType.IsSupportDocFile) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        文档识别
                    </div>
                    <%} %>
                    <%if (userType.IsSupportBatch) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        批量识别
                    </div>
                    <%} %>
                    <%if (userType.MaxLoginCount > 1) { %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        多设备支持(<%=userType.MaxLoginCount %>台)
                    </div>
                    <%} %>
                    <div class="feature-item">
                        <span class="feature-icon">✓</span>
                        每日限额: <%=userType.LimitPerDayCount %>次
                    </div>
                </div>

                <div class="plan-pricing">
                    <div class="price-options" data-type="<%=typeHash %>">
                        <%
                            // 构建价格选项列表（参考UserUpgrade.aspx的逻辑）
                            var lstChargeType = new List<ChargeViewToUser>();
                            try
                            {
                                if (userType.ChargeTypes != null)
                                {
                                    foreach (var q in userType.ChargeTypes)
                                    {
                                        string strDesc = "";
                                        var price = q.GetPrice(userType.PerPrice, ref strDesc);
                                        var charge = new ChargeViewToUser()
                                        {
                                            Name = q.Name,
                                            Desc = strDesc,
                                            Price = (double)price,
                                            OriPrice = (double)q.OriPrice,
                                            IsDefault = q.IsDefault,
                                            Tag = q.Tag,
                                        };
                                        lstChargeType.Add(charge);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                // 如果价格计算失败，使用默认价格
                                lstChargeType.Add(new ChargeViewToUser
                                {
                                    Name = "一年",
                                    Desc = "",
                                    Price = 100,
                                    OriPrice = 100,
                                    IsDefault = true,
                                    Tag = ""
                                });
                            }

                            foreach (var charge in lstChargeType)
                            {
                                // 修正折扣计算：折扣 = 现价/原价 * 10，例如0.3折表示现价是原价的30%
                                var discount = charge.OriPrice > charge.Price ? Math.Round((charge.Price / charge.OriPrice) * 10, 1) : 0;
                        %>
                        <div class="price-option <%=charge.IsDefault ? "selected" : "" %>"
                             data-months="<%=charge.Name %>"
                             data-price="<%=charge.Price %>"
                             data-remark="<%=charge.Desc %>">
                            <div class="duration"><%=charge.Name %></div>
                            <div class="price">
                                <%if (charge.OriPrice > charge.Price) { %>
                                <span class="original-price">¥<%=charge.OriPrice.ToString("F0") %></span>
                                <%} %>
                                <span class="current-price">¥<%=charge.Price.ToString("F0") %></span>
                                <%if (discount > 0) { %>
                                <span class="discount"><%=discount %>折</span>
                                <%} %>
                            </div>
                        </div>
                        <%
                            }
                        %>
                    </div>
                </div>

                <a href="javascript:void(0)"
                   class="upgrade-btn btn-<%=userType.Type.ToString() == "个人版" ? "personal" : (userType.Type.ToString() == "专业版" ? "professional" : "flagship") %>"
                   onclick="upgradeToType('<%=typeHash %>', '<%=userType.Type.ToString() %>')">
                    立即升级到<%=userType.Type.ToString() %>
                </a>
            </div>
            <%
                }
            %>
        </div>

        <div class="comparison-link">
            <a href="Version.aspx">查看详细功能对比 →</a>
        </div>
    </div>

    <%-- 将账号信息传递给JavaScript --%>
    <script type="text/javascript">
        var currentAccount = '<%=strAccount %>';
    </script>

    <script type="text/javascript">
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initPriceOptions();
        });

        // 初始化价格选项
        function initPriceOptions() {
            var priceContainers = document.querySelectorAll('.price-options');

            priceContainers.forEach(function(container) {
                var options = container.querySelectorAll('.price-option');

                options.forEach(function(option) {
                    option.addEventListener('click', function() {
                        // 移除同组其他选项的选中状态
                        options.forEach(function(opt) {
                            opt.classList.remove('selected');
                        });

                        // 添加当前选项的选中状态
                        this.classList.add('selected');
                    });
                });
            });
        }

        // 悬停效果已通过CSS处理，保持简洁专业的设计

        // 升级到指定类型
        function upgradeToType(typeHash, typeName) {
            var container = document.querySelector('.price-options[data-type="' + typeHash + '"]');
            if (!container) {
                alert('请选择升级方案');
                return;
            }

            var selectedOption = container.querySelector('.price-option.selected');
            if (!selectedOption) {
                alert('请选择升级时长');
                return;
            }

            var months = selectedOption.getAttribute('data-months');
            var price = selectedOption.getAttribute('data-price');
            var remark = selectedOption.getAttribute('data-remark');

            // 直接跳转，不需要二次确认

            // 跳转到支付页面
            var payUrl = 'UserUpgrade.aspx?type=' + typeHash + '&months=' + months + '&price=' + price;
            if (currentAccount && currentAccount.trim() !== '') {
                payUrl += '&account=' + encodeURIComponent(currentAccount);
            }

            window.location.href = payUrl;
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            // 移动端自动选择推荐方案
            if (window.innerWidth <= 768) {
                var recommendedCard = document.querySelector('.plan-card.recommended');
                if (recommendedCard) {
                    recommendedCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });

        // 页面加载时的特殊处理
        window.addEventListener('load', function() {
            // 如果URL中指定了type参数，自动滚动到对应的卡片
            var urlParams = new URLSearchParams(window.location.search);
            var selectedType = urlParams.get('type');

            if (selectedType) {
                var targetCard = document.querySelector('.price-options[data-type="' + selectedType + '"]');
                if (targetCard) {
                    var card = targetCard.closest('.plan-card');
                    if (card) {
                        card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        // 添加高亮效果
                        card.style.boxShadow = '0 0 20px rgba(0, 123, 255, 0.5)';
                        setTimeout(function() {
                            card.style.boxShadow = '';
                        }, 3000);
                    }
                }
            }
        });
    </script>
</asp:Content>
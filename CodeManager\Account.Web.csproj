﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F74FCA00-AE7C-45EB-8BFB-55D7B2BB5DB4}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Account.Web</RootNamespace>
    <AssemblyName>Account.Web</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <TargetFrameworkProfile />
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress>
    </Use64BitIISExpress>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <FilesToIncludeForPublish>OnlyFilesToRunTheApp</FilesToIncludeForPublish>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="FreeSql, Version=3.2.680.0, Culture=neutral, PublicKeyToken=a33928e5d4a4b39c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\FreeSql.3.2.680\lib\net451\FreeSql.dll</HintPath>
    </Reference>
    <Reference Include="FreeSql.Provider.Sqlite, Version=3.2.680.0, Culture=neutral, PublicKeyToken=5800863e689c9dd8, processorArchitecture=MSIL">
      <HintPath>..\..\packages\FreeSql.Provider.Sqlite.3.2.680\lib\net45\FreeSql.Provider.Sqlite.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4, Version=1.2.16.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\..\packages\K4os.Compression.LZ4.1.2.16\lib\net46\K4os.Compression.LZ4.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Hash.xxHash, Version=1.0.7.0, Culture=neutral, PublicKeyToken=32cd54395057cec3, processorArchitecture=MSIL">
      <HintPath>..\..\packages\K4os.Hash.xxHash.1.0.7\lib\net46\K4os.Hash.xxHash.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=1.2.10.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\DLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.4.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.SignalR.Core.2.4.3\lib\net45\Microsoft.AspNet.SignalR.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.SystemWeb, Version=2.4.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.SignalR.SystemWeb.2.4.3\lib\net45\Microsoft.AspNet.SignalR.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.2.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.Host.SystemWeb.2.1.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.Security.2.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="Qiniu, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Qiniu.SDK.8.0.0\lib\net45\Qiniu.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis.StrongName, Version=1.1.608.0, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\..\packages\StackExchange.Redis.StrongName.1.1.608\lib\net46\StackExchange.Redis.StrongName.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="AboutUs.aspx" />
    <Content Include="Download.aspx" />
    <Content Include="MemberUpgrade.aspx" />
    <Content Include="Upgrade.aspx" />
    <Content Include="UserAgreement.aspx" />
    <Content Include="PrivacyPolicy.aspx" />
    <Content Include="FAQ.aspx" />
    <Content Include="PayList.aspx" />
    <Content Include="ocr\ancient_ocr.html" />
    <Content Include="ocr\excel2pdf.html" />
    <Content Include="ocr\handwritten_ocr.html" />
    <Content Include="ocr\image2pdf.html" />
    <Content Include="ocr\index.html" />
    <Content Include="ocr\pdf2excel.html" />
    <Content Include="ocr\pdf2jpg.html" />
    <Content Include="ocr\pdf2markdown.html" />
    <Content Include="ocr\pdf2ppt.html" />
    <Content Include="ocr\pdf2word.html" />
    <Content Include="ocr\static\css\048266ae42f362cd.css" />
    <Content Include="ocr\static\css\09a1ad1c616d180a.css" />
    <Content Include="ocr\static\css\2d170f7bb9587ee4.css" />
    <Content Include="ocr\static\css\71899eb9acb65263.css" />
    <Content Include="ocr\static\css\97e00c3eababac2c.css" />
    <Content Include="ocr\static\css\bdaf914ad231fab2.css" />
    <Content Include="ocr\static\css\bf406b4dfe4a88f7.css" />
    <Content Include="ocr\static\css\ccb4941a4753d970.css" />
    <Content Include="ocr\static\css\f37d8a99f255f819.css" />
    <Content Include="ocr\static\image\Advertising%402x.png" />
    <Content Include="ocr\static\image\float%402x.png" />
    <Content Include="ocr\static\image\guide_bg%402x.png" />
    <Content Include="ocr\static\image\ic-label%402x.png" />
    <Content Include="ocr\static\image\icon-exchange.svg" />
    <Content Include="ocr\static\image\nodata.svg" />
    <Content Include="ocr\static\image\pic-jpg.svg" />
    <Content Include="ocr\static\image\pic-md.svg" />
    <Content Include="ocr\static\image\pic-pdf.svg" />
    <Content Include="ocr\static\image\pic-ppt.svg" />
    <Content Include="ocr\static\image\pic-simplead-bg%402x.png" />
    <Content Include="ocr\static\image\pic-xls.svg" />
    <Content Include="ocr\static\image\pic_banner%402x.png" />
    <Content Include="ocr\static\js\121-b6d40ccfcd609a3d.js" />
    <Content Include="ocr\static\js\171.4dffd9a11b491d6d.js" />
    <Content Include="ocr\static\js\188-3e55a14c02731598.js" />
    <Content Include="ocr\static\js\387-5665b26f1097c66c.js" />
    <Content Include="ocr\static\js\676-58ab10dc70b27a01.js" />
    <Content Include="ocr\static\js\680.055fc84287b81985.js" />
    <Content Include="ocr\static\js\75fc9c18-2e9ae03a475db518.js" />
    <Content Include="ocr\static\js\811-ad31b00c33b005e1.js" />
    <Content Include="ocr\static\js\937-f867bcf63c8b973c.js" />
    <Content Include="ocr\static\js\97-f3427726e942417a.js" />
    <Content Include="ocr\static\js\ancient_ocr-199c445f1e7013e2.js" />
    <Content Include="ocr\static\js\framework-bb5c596eafb42b22.js" />
    <Content Include="ocr\static\js\handwritten_ocr-dcc8cf1f5bebeb7f.js" />
    <Content Include="ocr\static\js\index-0c964641cb03d24c.js" />
    <Content Include="ocr\static\js\leads.js" />
    <Content Include="ocr\static\js\main-ab39c4ec2bce69ad.js" />
    <Content Include="ocr\static\js\polyfills-c67a75d1b6f99dc8.js" />
    <Content Include="ocr\static\js\table-c7ac0526c751e892.js" />
    <Content Include="ocr\static\js\text_recognize-c0949d51319d3d2e.js" />
    <Content Include="ocr\static\js\webpack-9e2dac2c71e11463.js" />
    <Content Include="ocr\static\js\[service]-238939d66f4ab4d3.js" />
    <Content Include="ocr\static\js\_app-d736fe5cc417ac5c.js" />
    <Content Include="ocr\static\image\pic-doc.svg" />
    <Content Include="ocr\static\picture\card-pic-2%402x.png" />
    <Content Include="ocr\static\picture\download-763bd3068d2349d78fb3b3558b84c704.jpg" />
    <Content Include="ocr\static\picture\download-ec8f6f0a56f84e6396f3c1475d6596f9.jpg" />
    <Content Include="ocr\static\picture\ic-close.svg" />
    <Content Include="ocr\static\picture\icon-arrow-right.svg" />
    <Content Include="ocr\static\picture\icon-circle-check.svg" />
    <Content Include="ocr\static\picture\icon-exchange.svg" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-excel-pdf402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-jpg-pdf402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-jpg-table402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-jpg-txt402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-pdf-excel402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-pdf-jpg402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-pdf-md402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-pdf-ppt402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-pdf-word402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-word-jpg402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Fhome2Ficon-word-pdf402x.png_96_75.png" />
    <Content Include="ocr\static\picture\image-2Ficons2Flogo-White402x.png_384_75.png" />
    <Content Include="ocr\static\picture\image-2Fimages2Fbusiness_license.png_3840_75.png" />
    <Content Include="ocr\static\picture\image-2Fimages2Fnetwork_supervisor.png_384_75.png" />
    <Content Include="ocr\static\picture\image-2Fimages2Freport_center.png_384_75.png" />
    <Content Include="ocr\static\picture\image-2Fimages2Ftext2Fcard-pic-language402x.png_828_75.png" />
    <Content Include="ocr\static\picture\image-2Fimages2Ftext2Fcard-pic-percent402x.png_828_75.png" />
    <Content Include="ocr\static\picture\image-2Fimages2Ftext2Fcard-pic-scene402x.png_828_75.png" />
    <Content Include="ocr\static\picture\image-2Fimages2Fwechat-group-20240511.png_384_75.png" />
    <Content Include="ocr\static\picture\logo-footer-title.svg" />
    <Content Include="ocr\static\picture\nav-tel.svg" />
    <Content Include="ocr\static\picture\nodata.svg" />
    <Content Include="ocr\static\picture\pic-doc.svg" />
    <Content Include="ocr\static\picture\pic-jpg.svg" />
    <Content Include="ocr\static\picture\pic-md.svg" />
    <Content Include="ocr\static\picture\pic-pdf.svg" />
    <Content Include="ocr\static\picture\pic-ppt.svg" />
    <Content Include="ocr\static\picture\pic-xls.svg" />
    <Content Include="ocr\static\picture\wechat.7f7315cd.png" />
    <Content Include="ocr\table_frame.html" />
    <Content Include="ocr\table_noframe.html" />
    <Content Include="ocr\table.html" />
    <Content Include="ocr\text_accurate.html" />
    <Content Include="ocr\text_recognize.html" />
    <Content Include="ocr\word2jpg.html" />
    <Content Include="ocr\word2pdf.html" />
    <Content Include="pay\360\Qh360Sdk\x64\360Base64.dll" />
    <Content Include="pay\360\Qh360Sdk\x64\360Lysdk64.dll" />
    <Content Include="pay\360\Qh360Sdk\x64\360NetBase64.dll" />
    <Content Include="pay\360\Qh360Sdk\x64\360Util64.dll" />
    <Content Include="pay\360\Qh360Sdk\x86\360Base.dll" />
    <Content Include="pay\360\Qh360Sdk\x86\360Lysdk.dll" />
    <Content Include="pay\360\Qh360Sdk\x86\360NetBase.dll" />
    <Content Include="pay\360\Qh360Sdk\x86\360Util.dll" />
    <Content Include="PrivacyPolicy.aspx" />
    <Content Include="site\css\site.css" />
    <Content Include="site\image\logo-s.png" />
    <Content Include="site\js\index.min1.js" />
    <Content Include="site\js\site.js" />
    <Content Include="Ocr.aspx" />
    <Content Include="static\js\auth-common.js" />
    <Content Include="static\js\autocdn.js" />
    <Content Include="static\pay\wx\137.95.png" />
    <Content Include="static\pay\wx\137.96.png" />
    <Content Include="static\pay\wx\137.97.png" />
    <Content Include="static\pay\wx\137.98.png" />
    <Content Include="static\pay\wx\137.99.png" />
    <Content Include="static\pay\wx\138.00.png" />
    <Content Include="static\pay\wx\157.95.png" />
    <Content Include="static\pay\wx\157.96.png" />
    <Content Include="static\pay\wx\157.97.png" />
    <Content Include="static\pay\wx\157.98.png" />
    <Content Include="static\pay\wx\157.99.png" />
    <Content Include="static\pay\wx\158.00.png" />
    <Content Include="static\pay\wx\237.95.png" />
    <Content Include="static\pay\wx\237.96.png" />
    <Content Include="static\pay\wx\237.97.png" />
    <Content Include="static\pay\wx\237.98.png" />
    <Content Include="static\pay\wx\237.99.png" />
    <Content Include="static\pay\wx\238.00.png" />
    <Content Include="static\pay\wx\35.95.png" />
    <Content Include="static\pay\wx\35.96.png" />
    <Content Include="static\pay\wx\35.97.png" />
    <Content Include="static\pay\wx\35.98.png" />
    <Content Include="static\pay\wx\35.99.png" />
    <Content Include="static\pay\wx\36.00.png" />
    <Content Include="static\pay\wx\367.95.png" />
    <Content Include="static\pay\wx\367.96.png" />
    <Content Include="static\pay\wx\367.97.png" />
    <Content Include="static\pay\wx\367.98.png" />
    <Content Include="static\pay\wx\367.99.png" />
    <Content Include="static\pay\wx\368.00.png" />
    <Content Include="static\pay\wx\567.95.png" />
    <Content Include="static\pay\wx\567.96.png" />
    <Content Include="static\pay\wx\567.97.png" />
    <Content Include="static\pay\wx\567.98.png" />
    <Content Include="static\pay\wx\567.99.png" />
    <Content Include="static\pay\wx\568.00.png" />
    <Content Include="static\pay\wx\57.95.png" />
    <Content Include="static\pay\wx\57.96.png" />
    <Content Include="static\pay\wx\57.97.png" />
    <Content Include="static\pay\wx\57.98.png" />
    <Content Include="static\pay\wx\57.99.png" />
    <Content Include="static\pay\wx\58.00.png" />
    <Content Include="Tool.aspx" />
    <Content Include="NewUser.aspx" />
    <Content Include="CSS\bootstrap.min.css" />
    <Content Include="Default.aspx" />
    <Content Include="DescNew.aspx" />
    <Content Include="Product.aspx" />
    <Content Include="privacy.html" />
    <Content Include="site\css\animate.css" />
    <Content Include="site\css\bootstrap.min.css" />
    <Content Include="site\css\global.css" />
    <Content Include="site\css\index.css" />
    <Content Include="site\css\index1.css" />
    <Content Include="site\css\new-base.css" />
    <Content Include="site\image\banner\banner_1.png" />
    <Content Include="site\image\banner\banner_2.jpg" />
    <Content Include="site\image\banner\banner_4.jpg" />
    <Content Include="site\image\banner\m_1.png" />
    <Content Include="site\image\banner\m_2.png" />
    <Content Include="site\image\banner\m_4.png" />
    <Content Include="site\image\icon\banner_1.png" />
    <Content Include="site\image\icon\banner_2.png" />
    <Content Include="site\image\icon\error.png" />
    <Content Include="site\image\icon\im.svg" />
    <Content Include="site\image\icon\p_1.png" />
    <Content Include="site\image\icon\p_11.png" />
    <Content Include="site\image\icon\p_2.png" />
    <Content Include="site\image\icon\p_21.png" />
    <Content Include="site\image\icon\p_3.png" />
    <Content Include="site\image\icon\p_4.png" />
    <Content Include="site\image\icon\qq.svg" />
    <Content Include="site\image\icon\school_1.png" />
    <Content Include="site\image\icon\school_11.png" />
    <Content Include="site\image\icon\school_2.png" />
    <Content Include="site\image\icon\school_3.png" />
    <Content Include="site\image\icon\school_4.png" />
    <Content Include="site\image\icon\school_5.png" />
    <Content Include="site\image\icon\school_6.png" />
    <Content Include="site\image\icon\s_1.png" />
    <Content Include="site\image\icon\s_10.png" />
    <Content Include="site\image\icon\s_2.png" />
    <Content Include="site\image\icon\s_3.png" />
    <Content Include="site\image\icon\s_4.png" />
    <Content Include="site\image\icon\s_5.png" />
    <Content Include="site\image\icon\s_6.png" />
    <Content Include="site\image\icon\s_7.png" />
    <Content Include="site\image\icon\s_8.png" />
    <Content Include="site\image\icon\s_9.png" />
    <Content Include="site\image\icon\vector.png" />
    <Content Include="site\image\logo.png" />
    <Content Include="site\js\jquery-3.6.0.min.js" />
    <Content Include="site\js\jquery.scrollAnimations.min.js" />
    <Content Include="static\css\pay.css" />
    <Content Include="static\css\upgrade.css" />
    <Content Include="static\image\bg3.jpg" />
    <Content Include="static\image\icon_hint_blue.svg" />
    <Content Include="static\image\pay\alipay.jpg" />
    <Content Include="static\image\pay\alipay.png" />
    <Content Include="static\image\pay\alipay_lim[limit].png" />
    <Content Include="static\image\pay\guide.png" />
    <Content Include="static\image\pay\guide_lim[limit].png" />
    <Content Include="static\image\pay\loading.gif" />
    <Content Include="static\image\pay\pay_down_ico.png" />
    <Content Include="static\image\pay\unipay.jpg" />
    <Content Include="static\image\pay\use_1.png" />
    <Content Include="static\image\pay\use_1_lim[limit].png" />
    <Content Include="static\image\pay\use_2.png" />
    <Content Include="static\image\pay\use_2_lim[limit].png" />
    <Content Include="static\image\pay\wave.png" />
    <Content Include="static\image\pay\wave_lim[limit].png" />
    <Content Include="static\image\pay\wechat-pay.png" />
    <Content Include="static\image\pay\wechat-pay_lim[limit].png" />
    <Content Include="static\image\pay\weixin.jpg" />
    <Content Include="static\image\radio-checked1.svg" />
    <Content Include="static\image\radio-unchecked.svg" />
    <Content Include="static\image\vip1.png" />
    <Content Include="static\image\vip_0.png" />
    <Content Include="static\image\vip_1.png" />
    <Content Include="static\image\vip_10.png" />
    <Content Include="static\image\vip_2.png" />
    <Content Include="static\image\vip_3.png" />
    <Content Include="static\image\vip_4.png" />
    <Content Include="static\image\vip_5.png" />
    <Content Include="static\image\vip_6.png" />
    <Content Include="static\image\vip_7.png" />
    <Content Include="static\image\vip_8.png" />
    <Content Include="static\image\vip_9.png" />
    <Content Include="static\js\buypack.js" />
    <Content Include="static\js\translate.js" />
    <Content Include="tool\Color.aspx" />
    <Content Include="tool\Calc.aspx" />
    <Content Include="tool\Count.aspx" />
    <Content Include="tool\Diff.aspx" />
    <Content Include="tool\Encode.aspx" />
    <Content Include="tool\static\css\regex.css" />
    <Content Include="tool\static\css\timespan.css" />
    <Content Include="tool\static\js\regex.js" />
    <Content Include="tool\static\js\timespan.js" />
    <Content Include="tool\Regex.aspx" />
    <Content Include="tool\Timespan.aspx" />
    <Content Include="tool\Json.aspx" />
    <Content Include="tool\static\css\app-color.css" />
    <Content Include="tool\static\css\bootstrap.min.css" />
    <Content Include="tool\static\css\codemirror.css" />
    <Content Include="tool\static\css\feedback.css" />
    <Content Include="tool\static\css\index-05843bc3.css" />
    <Content Include="tool\static\css\index-06d17005.css" />
    <Content Include="tool\static\css\index-571fbde8.css" />
    <Content Include="tool\static\css\index-64ab305f.css" />
    <Content Include="tool\static\css\index-b05f4643.css" />
    <Content Include="tool\static\css\index-b078aaba.css" />
    <Content Include="tool\static\css\index-b6da8fdc.css" />
    <Content Include="tool\static\css\item.css" />
    <Content Include="tool\static\css\mergely.css" />
    <Content Include="tool\static\font\glyphicons-halflings-regular.svg" />
    <Content Include="tool\static\image\json-demo.jpg" />
    <Content Include="tool\static\image\mix\10_x.png" />
    <Content Include="tool\static\image\mix\1_x.png" />
    <Content Include="tool\static\image\mix\x_2.png" />
    <Content Include="tool\static\image\mix\x_y.png" />
    <Content Include="tool\static\image\mix\x_y_sqrt.png" />
    <Content Include="tool\static\image\search_grey600_24dp.png" />
    <Content Include="tool\static\image\to-top-hover.png" />
    <Content Include="tool\static\image\to-top.png" />
    <Content Include="tool\static\image\yellowtipclose.png" />
    <Content Include="tool\static\js\active-line.js" />
    <Content Include="tool\static\js\backbone-events.min.js" />
    <Content Include="tool\static\js\beautify-html.js" />
    <Content Include="tool\static\js\beautify-vk.js" />
    <Content Include="tool\static\js\clipboard.min.js" />
    <Content Include="tool\static\js\codemirror-json.js" />
    <Content Include="tool\static\js\codemirror.js" />
    <Content Include="tool\static\js\colorpicker.min.js" />
    <Content Include="tool\static\js\content-script-8cbbc92c.js" />
    <Content Include="tool\static\js\content-script-cc14877d.js" />
    <Content Include="tool\static\js\content-script-f0705236.js" />
    <Content Include="tool\static\js\core.js" />
    <Content Include="tool\static\js\fh-config.js" />
    <Content Include="tool\static\js\fml.js" />
    <Content Include="tool\static\js\formatting.js" />
    <Content Include="tool\static\js\he.js" />
    <Content Include="tool\static\js\index-065782ae.js" />
    <Content Include="tool\static\js\index-3f78b8e0.js" />
    <Content Include="tool\static\js\index-8ae12415.js" />
    <Content Include="tool\static\js\index-8df1e5e2.js" />
    <Content Include="tool\static\js\index-9e1ed2a4.js" />
    <Content Include="tool\static\js\index-ab748982.js" />
    <Content Include="tool\static\js\index-cf567dc8.js" />
    <Content Include="tool\static\js\index-color.js" />
    <Content Include="tool\static\js\item.js" />
    <Content Include="tool\static\js\javascript.js" />
    <Content Include="tool\static\js\jquery-3.3.1.min.js" />
    <Content Include="tool\static\js\jquery.1.7.1.min.js" />
    <Content Include="tool\static\js\jquery.colorpicker.js" />
    <Content Include="tool\static\js\jquery.qrcode.min.js" />
    <Content Include="tool\static\js\json-diff.js" />
    <Content Include="tool\static\js\json-lint.js" />
    <Content Include="tool\static\js\json-patch-duplex.min.js" />
    <Content Include="tool\static\js\json-source-map.js" />
    <Content Include="tool\static\js\matchbrackets.js" />
    <Content Include="tool\static\js\mergely.js" />
    <Content Include="tool\static\js\navbar.js" />
    <Content Include="tool\static\js\placeholder.js" />
    <Content Include="tool\static\js\prism.js" />
    <Content Include="tool\static\js\require.js" />
    <Content Include="tool\static\js\vue.js" />
    <Content Include="tool\static\js\zxing.min.js" />
    <Content Include="tool\static\picture\2014-11-22_21-00-02_1.png" />
    <Content Include="tool\static\picture\2015-06-06_17-15-04_5.gif" />
    <Content Include="tool\static\picture\2015-06-06_17-17-22_8.gif" />
    <Content Include="tool\static\picture\2015-06-06_17-20-27_4.gif" />
    <Content Include="tool\static\picture\2015-06-06_17-24-36_2.gif" />
    <Content Include="tool\static\picture\2015-06-06_17-25-01_9.gif" />
    <Content Include="tool\static\picture\2015-06-06_17-27-48_4.gif" />
    <Content Include="tool\static\picture\2015-12-26_22-51-14_9.png" />
    <Content Include="tool\static\picture\2015-12-26_22-51-26_4.png" />
    <Content Include="tool\static\picture\2015-12-26_22-51-33_1.png" />
    <Content Include="tool\static\picture\2015-12-26_22-51-42_7.png" />
    <Content Include="tool\static\picture\2015-12-26_22-51-50_4.png" />
    <Content Include="tool\static\picture\2015-12-26_22-51-58_9.png" />
    <Content Include="tool\static\picture\2016-01-09_09-01-52_2.png" />
    <Content Include="tool\static\picture\2016-01-09_09-04-36_4.gif" />
    <Content Include="tool\static\picture\2016-07-04_13-55-43_7.png" />
    <Content Include="tool\static\picture\2016-07-04_14-21-38_9.gif" />
    <Content Include="tool\static\picture\2016-09-13_12-19-26_9.png" />
    <Content Include="tool\static\picture\2016-09-13_12-19-55_8.png" />
    <Content Include="tool\static\picture\2016-12-08_17-33-53_6.gif" />
    <Content Include="tool\static\picture\2016-12-21_16-02-13_8.jpeg" />
    <Content Include="tool\static\picture\2017-01-20_14-30-13_7.png" />
    <Content Include="tool\static\picture\2017-01-20_14-31-26_4.png" />
    <Content Include="tool\static\picture\2017-01-20_14-37-08_8.png" />
    <Content Include="tool\static\picture\2017-03-06_11-51-30_6.jpg" />
    <Content Include="tool\static\picture\2017-03-14_16-10-57_0.png" />
    <Content Include="tool\static\picture\2018-03-21_15-08-40_1.png" />
    <Content Include="tool\static\picture\2018-03-21_15-10-29_6.png" />
    <Content Include="tool\static\picture\2018-03-21_15-16-35_9.png" />
    <Content Include="tool\static\picture\2018-03-21_15-17-28_2.jpeg" />
    <Content Include="tool\static\picture\2018-03-23_17-05-02_4.png" />
    <Content Include="tool\static\picture\2018-05-22_14-16-37_8.png" />
    <Content Include="tool\static\picture\2018-11-19_16-51-17_8.gif" />
    <Content Include="tool\static\picture\2019-04-18_11-31-48_4.png" />
    <Content Include="tool\static\picture\2019-04-18_11-38-08_8.gif" />
    <Content Include="tool\static\picture\2019-04-18_11-39-02_0.gif" />
    <Content Include="tool\static\picture\2019-04-18_11-40-09_1.gif" />
    <Content Include="tool\static\picture\2019-06-19_18-00-29_1.png" />
    <Content Include="tool\static\picture\2019-06-27_15-32-56_3.png" />
    <Content Include="tool\static\picture\2019-06-27_15-33-13_2.png" />
    <Content Include="tool\static\picture\2019-06-27_15-34-12_4.png" />
    <Content Include="tool\static\picture\2019-06-27_15-34-31_4.png" />
    <Content Include="tool\static\picture\2020-01-15_14-58-03_2.png" />
    <Content Include="tool\static\picture\2020-01-15_14-58-52_8.png" />
    <Content Include="tool\static\picture\2020-01-15_15-00-27_7.png" />
    <Content Include="tool\static\picture\2020-01-15_15-08-41_6.jpeg" />
    <Content Include="tool\static\picture\2020-02-26_19-04-14_1.png" />
    <Content Include="tool\static\picture\2020-02-26_19-04-43_5.png" />
    <Content Include="tool\static\picture\2020-02-26_19-04-48_3.png" />
    <Content Include="tool\static\picture\2020-03-09_10-28-48_6.png" />
    <Content Include="tool\static\picture\2020-03-09_10-30-08_4.png" />
    <Content Include="tool\static\picture\2020-03-09_10-43-23_9.png" />
    <Content Include="tool\static\picture\2020-03-09_10-46-53_7.png" />
    <Content Include="tool\static\picture\2020-04-01_19-05-06_8.png" />
    <Content Include="tool\static\picture\2020-04-01_19-05-13_6.png" />
    <Content Include="tool\static\picture\2020-04-14_11-21-58_9.png" />
    <Content Include="tool\static\picture\2020-05-27_11-59-59_3.png" />
    <Content Include="tool\static\picture\2022-01-13_10-35-13_4.png" />
    <Content Include="tool\static\picture\close-icon.png" />
    <Content Include="tool\static\picture\fe-128.png" />
    <Content Include="tool\static\picture\fe-16.png" />
    <Content Include="tool\static\picture\fe-48.png" />
    <Content Include="tool\static\picture\FeHelper-social.svg" />
    <Content Include="tool\static\picture\FeHelper-social1.svg" />
    <Content Include="tool\static\picture\fh-allinone.jpg" />
    <Content Include="tool\static\picture\h-line.gif" />
    <Content Include="tool\static\picture\logo.jpeg" />
    <Content Include="tool\static\picture\pkgccpejnmalmdinmhkkfafefagiiiad-Google20Chrome_red_blue.svg" />
    <Content Include="tool\static\picture\pkgccpejnmalmdinmhkkfafefagiiiad-Google20Chrome_red_blue1.svg" />
    <Content Include="tool\static\picture\pkgccpejnmalmdinmhkkfafefagiiiad-Google20Chrome_red_blue2.svg" />
    <Content Include="ToPay.aspx" />
    <Content Include="Upgrade.aspx" />
    <Content Include="UserAgreement.aspx" />
    <Content Include="UserUpgrade.aspx" />
    <Content Include="static\css\main.css" />
    <Content Include="static\image\icon.png" />
    <Content Include="UserReg.aspx" />
    <Content Include="UserForgetPwd.aspx" />
    <Content Include="Desc.aspx" />
    <Content Include="AllUser.aspx" />
    <Content Include="CSS\style.css" />
    <Content Include="DB\Code.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Detail.aspx" />
    <Content Include="IPList.aspx" />
    <Content Include="math\auto-render.min.js" />
    <Content Include="math\contextMenu.js" />
    <Content Include="math\copy.png" />
    <Content Include="math\katex.min.css" />
    <Content Include="math\katex.min.js" />
    <Content Include="math\refresh.png" />
    <Content Include="math\view.aspx" />
    <Content Include="math\view.html" />
    <Content Include="Pay.aspx" />
    <Content Include="Server.aspx" />
    <Content Include="static\js\clearbox.js" />
    <Content Include="static\js\clearbox\config\default\cb_config.js" />
    <Content Include="static\js\clearbox\config\default\cb_style.css" />
    <Content Include="static\js\clearbox\config\default\pic\blank.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_dl.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_max.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_next.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_prev.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_rot_l.gif" />
    <Content Include="static\js\clearbox\config\default\pic\btm_rot_r.gif" />
    <Content Include="static\js\clearbox\config\default\pic\close.png" />
    <Content Include="static\js\clearbox\config\default\pic\next.png" />
    <Content Include="static\js\clearbox\config\default\pic\no_flash.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_html.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_iframe.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_image.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_inner.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_quicktime.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmedia.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediaavi.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediamp3.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediampg.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediawav.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediawma.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_winmediawmv.gif" />
    <Content Include="static\js\clearbox\config\default\pic\no_youtube.gif" />
    <Content Include="static\js\clearbox\config\default\pic\pause.png" />
    <Content Include="static\js\clearbox\config\default\pic\prev.png" />
    <Content Include="static\js\clearbox\config\default\pic\start.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_btm.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_btmleft.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_btmright.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_left.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_right.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_top.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_topleft.png" />
    <Content Include="static\js\clearbox\config\default\pic\s_topright.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\cb_config.js" />
    <Content Include="static\js\clearbox\config\grow_transparent\cb_style.css" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\blank.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_dl.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_max.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_next.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_prev.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_rot_l.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\btm_rot_r.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\close.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\next.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_flash.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_html.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_iframe.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_image.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_inner.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_quicktime.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmedia.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediaavi.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediamp3.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediampg.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediawav.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediawma.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_winmediawmv.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\no_youtube.gif" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\pause.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\prev.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\start.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_btm.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_btmleft.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_btmright.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_left.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_right.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_top.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_topleft.png" />
    <Content Include="static\js\clearbox\config\grow_transparent\pic\s_topright.png" />
    <Content Include="static\js\clearbox\core\cb_core.js" />
    <Content Include="static\js\clearbox\language\en\cb_language.js" />
    <Content Include="static\js\clearbox\language\fr\cb_language.js" />
    <Content Include="static\js\clearbox\language\hu\cb_language.js" />
    <Content Include="static\js\clearbox\language\tr\cb_language.js" />
    <Content Include="Code.aspx" />
    <Content Include="Global.asax" />
    <Content Include="Mail.aspx" />
    <Content Include="static\js\clearbox\language\cn\cb_language.js" />
    <Content Include="static\css\0.8b61c27fbd03eea84593.css" />
    <Content Include="static\css\19.8b61c27fbd03eea84593.css" />
    <Content Include="static\css\external20210513-26-1nfet2i.css" />
    <Content Include="static\css\status_manifest.css" />
    <Content Include="static\image\externalities-2428cb8b890516d7bf8ee2939dbd78ad6428890b546c7447f5892524e11e94b1.png" />
    <Content Include="static\image\externalities_dark-3761258b4ae696df202d52c2c4125ff1507f92ae547a059f7477de2a89193617.png" />
    <Content Include="static\image\galaxy_new-032f6db4d8a5770c6bdf21369a587ead1e67a873588825729a591f332b320743.jpg" />
    <Content Include="static\js\api.js" />
    <Content Include="static\js\bowser.js" />
    <Content Include="static\js\common.chunk.js" />
    <Content Include="static\js\components-1ac25bc93cb7cb0637f2.chunk.js" />
    <Content Include="static\js\globals.chunk.js" />
    <Content Include="static\js\highstock.min.js" />
    <Content Include="static\js\polyfill.min.js" />
    <Content Include="static\js\runtime.js" />
    <Content Include="static\js\status-52d317a9a05694423231.chunk.js" />
    <Content Include="static\js\status_common-b059787fd2480825e068a19542051bd88613ab38786324ccf188976e76e013b5.js" />
    <Content Include="static\js\status_manifest.js" />
    <Content Include="Status.aspx" />
    <Content Include="file\view.html" />
    <Content Include="Login.aspx" />
    <Content Include="User.aspx" />
    <Content Include="Version.aspx" />
    <Content Include="voice\view.html" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="View.aspx" />
    <Content Include="RCode.aspx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\AuthHelper.cs" />
    <Compile Include="Common\CryptoService.cs" />
    <Compile Include="Common\LanguagePathModule.cs" />
    <Compile Include="Common\LanguageService.cs" />
    <Compile Include="Common\PasswordService.cs" />
    <Compile Include="Common\ResponseService.cs" />
    <Compile Include="Common\SecurityService.cs" />
    <Compile Include="Common\UnifiedUserService.cs" />
    <Compile Include="Common\UrlService.cs" />
    <Compile Include="Common\ValidationService.cs" />
    <Compile Include="MemberUpgrade.aspx.cs">
      <DependentUpon>MemberUpgrade.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="OcrProcessHelper.cs" />
    <Compile Include="PayList.aspx.cs">
      <DependentUpon>PayList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PayList.aspx.designer.cs">
      <DependentUpon>PayList.aspx</DependentUpon>
    </Compile>
    <Compile Include="pay\360\Qh360SdkEnv.cs" />
    <Compile Include="pay\360\Qh360SdkHelper.cs" />
    <Compile Include="pay\360\Qh360SdkOrderRequest.cs" />
    <Compile Include="pay\360\Qh360SdkWrapper.cs" />
    <Compile Include="pay\360\Sdk360OrderContext.cs" />
    <Compile Include="pay\NewPayUtil.cs" />
    <Compile Include="pay\PayHelper.cs" />
    <Compile Include="BLL\PageTitleConst.cs" />
    <Compile Include="Common\CommonRequest.cs" />
    <Compile Include="Common\RequestExtensions.cs" />
    <Compile Include="Common\UserConst.cs" />
    <Compile Include="NewUser.aspx.cs">
      <DependentUpon>NewUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="NewUser.aspx.designer.cs">
      <DependentUpon>NewUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="AllUser.aspx.cs">
      <DependentUpon>AllUser.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AllUser.aspx.designer.cs">
      <DependentUpon>AllUser.aspx</DependentUpon>
    </Compile>
    <Compile Include="BLL\CommonHelper.cs" />
    <Compile Include="Code.ashx.cs">
      <DependentUpon>Code.ashx</DependentUpon>
    </Compile>
    <Compile Include="Code.aspx.cs">
      <DependentUpon>Code.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Code.aspx.designer.cs">
      <DependentUpon>Code.aspx</DependentUpon>
    </Compile>
    <Compile Include="Common\CommonTranslate.cs" />
    <Compile Include="Common\CommonValidateCode.cs" />
    <Compile Include="Common\QiNiuUpload.cs" />
    <Compile Include="Common\SqlChecker.cs" />
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DescNew.aspx.cs">
      <DependentUpon>DescNew.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="DescNew.aspx.designer.cs">
      <DependentUpon>DescNew.aspx</DependentUpon>
    </Compile>
    <Compile Include="MsgProcessHelper.cs" />
    <Compile Include="pay\PayQrcodeUtil.cs" />
    <Compile Include="OcrHub.cs" />
    <Compile Include="SignalRStartup.cs" />
    <Compile Include="Site.Master.cs">
      <DependentUpon>Site.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site.Master.designer.cs">
      <DependentUpon>Site.Master</DependentUpon>
    </Compile>
    <Compile Include="tool\Tool.Master.cs">
      <DependentUpon>Tool.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="tool\Tool.Master.designer.cs">
      <DependentUpon>Tool.Master</DependentUpon>
    </Compile>
    <Compile Include="ToPay.aspx.cs">
      <DependentUpon>ToPay.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ToPay.aspx.designer.cs">
      <DependentUpon>ToPay.aspx</DependentUpon>
    </Compile>
    <Compile Include="Upgrade.aspx.cs">
      <DependentUpon>Upgrade.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Upgrade.aspx.designer.cs">
      <DependentUpon>Upgrade.aspx</DependentUpon>
    </Compile>
    <Compile Include="User.ashx.cs">
      <DependentUpon>User.ashx</DependentUpon>
    </Compile>
    <Compile Include="UserLoginInfoHelper.cs" />
    <Compile Include="UserUpgrade.aspx.cs">
      <DependentUpon>UserUpgrade.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserUpgrade.aspx.designer.cs">
      <DependentUpon>UserUpgrade.aspx</DependentUpon>
    </Compile>
    <Compile Include="FrpcEntity.cs" />
    <Compile Include="CodeEntity.cs" />
    <Compile Include="BLL\CodeHelper.cs" />
    <Compile Include="CommonEncryptHelper.cs" />
    <Compile Include="Common\SyncTask.cs" />
    <Compile Include="Common\WebClientExt.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="IPList.aspx.cs">
      <DependentUpon>IPList.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IPList.aspx.designer.cs">
      <DependentUpon>IPList.aspx</DependentUpon>
    </Compile>
    <Compile Include="Mail.aspx.cs">
      <DependentUpon>Mail.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Mail.aspx.designer.cs">
      <DependentUpon>Mail.aspx</DependentUpon>
    </Compile>
    <Compile Include="OPEntity.cs" />
    <Compile Include="Pay.ashx.cs">
      <DependentUpon>Pay.ashx</DependentUpon>
    </Compile>
    <Compile Include="Pay.aspx.cs">
      <DependentUpon>Pay.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Pay.aspx.designer.cs">
      <DependentUpon>Pay.aspx</DependentUpon>
    </Compile>
    <Compile Include="pay\PayUtil.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Server.aspx.cs">
      <DependentUpon>Server.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Server.aspx.designer.cs">
      <DependentUpon>Server.aspx</DependentUpon>
    </Compile>
    <Compile Include="Status.aspx.cs">
      <DependentUpon>Status.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Status.aspx.designer.cs">
      <DependentUpon>Status.aspx</DependentUpon>
    </Compile>
    <Compile Include="View.aspx.cs">
      <DependentUpon>View.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="View.aspx.designer.cs">
      <DependentUpon>View.aspx</DependentUpon>
    </Compile>
    <Compile Include="RCode.aspx.cs">
      <DependentUpon>RCode.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="RCode.aspx.designer.cs">
      <DependentUpon>RCode.aspx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\CommonLib\CommonLib.csproj">
      <Project>{fc03a7d4-8ef2-4dea-a15a-c099eb77b0eb}</Project>
      <Name>CommonLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Emali.Process.Common\Notice.Process.Common.csproj">
      <Project>{cd28617b-75e1-4a8f-a5f7-652b2876467d}</Project>
      <Name>Notice.Process.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\ImageLib\ImageLib.csproj">
      <Project>{b7e169a2-3104-40fb-9d1e-2ff911fa45e5}</Project>
      <Name>ImageLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\RegLib\RegLib.csproj">
      <Project>{8b3dcd3b-a171-4b02-9ab6-8af732c87305}</Project>
      <Name>RegLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Log4Net.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Pay.ashx" />
    <Content Include="Code.ashx" />
    <Content Include="math\fonts\KaTeX_AMS-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_AMS-Regular.woff" />
    <Content Include="math\fonts\KaTeX_AMS-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Bold.ttf" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Bold.woff" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Bold.woff2" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Caligraphic-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Fraktur-Bold.ttf" />
    <Content Include="math\fonts\KaTeX_Fraktur-Bold.woff" />
    <Content Include="math\fonts\KaTeX_Fraktur-Bold.woff2" />
    <Content Include="math\fonts\KaTeX_Fraktur-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Fraktur-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Fraktur-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Main-Bold.ttf" />
    <Content Include="math\fonts\KaTeX_Main-Bold.woff" />
    <Content Include="math\fonts\KaTeX_Main-Bold.woff2" />
    <Content Include="math\fonts\KaTeX_Main-BoldItalic.ttf" />
    <Content Include="math\fonts\KaTeX_Main-BoldItalic.woff" />
    <Content Include="math\fonts\KaTeX_Main-BoldItalic.woff2" />
    <Content Include="math\fonts\KaTeX_Main-Italic.ttf" />
    <Content Include="math\fonts\KaTeX_Main-Italic.woff" />
    <Content Include="math\fonts\KaTeX_Main-Italic.woff2" />
    <Content Include="math\fonts\KaTeX_Main-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Main-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Main-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Math-BoldItalic.ttf" />
    <Content Include="math\fonts\KaTeX_Math-BoldItalic.woff" />
    <Content Include="math\fonts\KaTeX_Math-BoldItalic.woff2" />
    <Content Include="math\fonts\KaTeX_Math-Italic.ttf" />
    <Content Include="math\fonts\KaTeX_Math-Italic.woff" />
    <Content Include="math\fonts\KaTeX_Math-Italic.woff2" />
    <Content Include="math\fonts\KaTeX_SansSerif-Bold.ttf" />
    <Content Include="math\fonts\KaTeX_SansSerif-Bold.woff" />
    <Content Include="math\fonts\KaTeX_SansSerif-Bold.woff2" />
    <Content Include="math\fonts\KaTeX_SansSerif-Italic.ttf" />
    <Content Include="math\fonts\KaTeX_SansSerif-Italic.woff" />
    <Content Include="math\fonts\KaTeX_SansSerif-Italic.woff2" />
    <Content Include="math\fonts\KaTeX_SansSerif-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_SansSerif-Regular.woff" />
    <Content Include="math\fonts\KaTeX_SansSerif-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Script-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Script-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Script-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Size1-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Size1-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Size1-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Size2-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Size2-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Size2-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Size3-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Size3-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Size3-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Size4-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Size4-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Size4-Regular.woff2" />
    <Content Include="math\fonts\KaTeX_Typewriter-Regular.ttf" />
    <Content Include="math\fonts\KaTeX_Typewriter-Regular.woff" />
    <Content Include="math\fonts\KaTeX_Typewriter-Regular.woff2" />
    <Content Include="UserMaster.Master" />
    <None Include="packages.config" />
    <Content Include="pay\360\Qh360Sdk\x64\cacert.dat" />
    <Content Include="pay\360\Qh360Sdk\x86\cacert.dat" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <Content Include="static\font\fontawesome-webfont-7b5a4320fba0d4c8f79327645b4b9cc875a2ec617a557e849b813918eb733499.ttf" />
    <Content Include="static\font\fontawesome-webfont-c812ddc9e475d3e65d68a6b3b589ce598a2a5babb7afc55477d59215c4a38a40.woff" />
    <Content Include="static\font\fontawesome-webfont-e219ece8f4d3e4ac455ef31cd3a7c7b5057ea68a109937fc26b03c6e99ee9322.eot" />
    <Content Include="static\font\ProximaNovaBold-27177fe9242acbe089276ee587feef781446667ffe9b6fdc5b7fe21ad73e12f3.ttf" />
    <Content Include="static\font\ProximaNovaBold-622ea489d20e12e691663f83217105e957e2d3d09703707d40155a29c06cc9d9.eot" />
    <Content Include="static\font\ProximaNovaBold-c8dc577ff7f76d2fc199843e38c04bb2e9fd15889421358d966a9f846c2ed1cd.woff" />
    <Content Include="static\font\ProximaNovaLight-0f094da9b301d03292f97db5544142a16f9f2ddf50af91d44753d9310c194c5f.ttf" />
    <Content Include="static\font\ProximaNovaLight-e642ffe82005c6208632538a557e7f5dccb835c0303b06f17f55ccf567907241.woff" />
    <Content Include="static\font\ProximaNovaLight-f0b2f7c12b6b87c65c02d3c1738047ea67a7607fd767056d8a2964cc6a2393f7.eot" />
    <Content Include="static\font\ProximaNovaRegular-2ee4c449a9ed716f1d88207bd1094e21b69e2818b5cd36b28ad809dc1924ec54.woff" />
    <Content Include="static\font\ProximaNovaRegular-366d17769d864aa72f27defaddf591e460a1de4984bb24dacea57a9fc1d14878.eot" />
    <Content Include="static\font\ProximaNovaRegular-a40a469edbd27b65b845b8000d47445a17def8ba677f4eb836ad1808f7495173.ttf" />
    <Content Include="static\font\ProximaNovaRegularIt-0bf83a850b45e4ccda15bd04691e3c47ae84fec3588363b53618bd275a98cbb7.eot" />
    <Content Include="static\font\ProximaNovaRegularIt-0c394ec7a111aa7928ea470ec0a67c44ebdaa0f93d1c3341abb69656cc26cbdd.woff" />
    <Content Include="static\font\ProximaNovaRegularIt-9e43859f8015a4d47d9eaf7bafe8d1e26e3298795ce1f4cdb0be0479b8a4605e.ttf" />
    <Content Include="static\font\ProximaNovaSemibold-09566917307251d22021a3f91fc646f3e45f8d095209bcd2cded8a1979f06e54.eot" />
    <Content Include="static\font\ProximaNovaSemibold-86724fb2152613d735ba47c3f47a9ad2424b898bea4bece213dacee40344f966.woff" />
    <Content Include="static\font\ProximaNovaSemibold-cf3e4eb7fbdf6fb83e526cc2a0141e55b01097e6e1abfd4cbdc3eda75d183f74.ttf" />
    <Content Include="Site.Master" />
    <Content Include="tool\static\font\glyphicons-halflings-regular.eot" />
    <Content Include="tool\static\font\glyphicons-halflings-regular.ttf" />
    <Content Include="tool\static\font\glyphicons-halflings-regular.woff" />
    <Content Include="tool\Tool.Master" />
    <Content Include="User.ashx" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>19224</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:19225/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net45\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net45\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net45\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net45\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
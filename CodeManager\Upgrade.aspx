<%@ Page Title="升级VIP会员" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="Upgrade.aspx.cs" Inherits="Account.Web.Upgrade" %>
<%@ Import Namespace="CommonLib" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%@ Import Namespace="System.Linq" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
<meta name="description" content="升级OCR文字识别助手VIP会员，享受更多专业功能，个人版、专业版、旗舰版、运维版、技术版多种选择">
<style>
.upgrade-container{max-width:1200px;margin:0 auto;padding:20px;padding-top:65px;background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);min-height:calc(100vh - 100px)}
.main-content{display:flex;gap:30px;align-items:flex-start}
.left-section{flex:2;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);border-radius:12px;padding:30px;box-shadow:0 4px 20px rgba(0,0,0,0.08);border:1px solid rgba(255,255,255,0.8)}
.right-section{flex:1;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);border-radius:12px;padding:30px;box-shadow:0 4px 20px rgba(0,0,0,0.08);border:1px solid rgba(255,255,255,0.8);position:sticky;top:20px}
.section-title{font-size:1.3rem;font-weight:bold;color:#333;margin-bottom:20px;display:flex;align-items:center;justify-content:space-between}
.version-tabs{display:flex;gap:15px;margin-bottom:30px;flex-wrap:wrap}
.version-tab{flex:1;min-width:120px;padding:16px 16px 20px;border:2px solid #e9ecef;border-radius:12px;text-align:center;cursor:pointer;transition:all 0.3s ease;background:white;position:relative;overflow:hidden}
.version-tab:hover{transform:translateY(-3px);box-shadow:0 6px 20px rgba(0,0,0,0.12)}
.version-tab.active{transform:translateY(-3px);box-shadow:0 8px 24px rgba(0,0,0,0.18);border-width:3px}
.version-tab.personal{border-color:#6666FF;backdrop-filter:blur(10px)}
.version-tab.personal:hover{border-color:#6666FF;background:rgba(102,102,255,0.08);box-shadow:0 6px 20px rgba(102,102,255,0.25)}
.version-tab.professional{border-color:#4B4B4B;backdrop-filter:blur(10px)}
.version-tab.professional:hover{border-color:#4B4B4B;background:rgba(75,75,75,0.08);box-shadow:0 6px 20px rgba(75,75,75,0.25)}
.version-tab.flagship{border-color:#E6D700;backdrop-filter:blur(10px)}
.version-tab.flagship:hover{border-color:#E6D700;background:rgba(255,235,193,0.15);box-shadow:0 6px 20px rgba(255,215,0,0.25)}
.version-recommend-badge{position:absolute;top:-2px;right:-2px;background:#ff4757;color:white;padding:4px 10px;border-radius:0 12px 0 12px;font-size:11px;font-weight:600;z-index:2;box-shadow:0 2px 6px rgba(255,71,87,0.4);letter-spacing:0.5px}
.version-audience{font-size:0.8rem;color:#666;margin-top:4px;font-style:italic}
.version-tab.personal.active{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);border-color:#6666FF;color:white;box-shadow:0 4px 12px rgba(102,102,255,0.3)}

.version-features-section.personal{background:transparent;border:1px solid rgba(102,102,255,0.03)}
.version-features-section.personal .features-header{background:transparent;border-bottom:none}
.version-features-section.personal .features-title{color:#4444BB;font-weight:600;font-size:17px}
.version-features-section.personal .features-compare-link{color:#6666FF}
.version-features-section.personal .features-compare-link:hover{color:#5555EE}
.version-features-section.personal .features-body{background:transparent}
.version-features-section.personal .feature-item:hover{background:rgba(102,102,255,0.04)}
.version-features-section.personal .feature-item i{color:#6666FF}
.version-features-section.personal .features-footer{background:transparent;border-top:none}
.version-features-section.personal .expand-btn{border-color:#6666FF;color:#6666FF;background:rgba(255,255,255,0.9)}
.version-features-section.personal .expand-btn:hover{border-color:#5555EE;color:#5555EE;background:rgba(255,255,255,1)}
.pricing-item.selected.personal{background:linear-gradient(135deg,rgba(102,102,255,0.12) 0%,rgba(102,102,255,0.18) 100%);border-color:#6666FF;color:#6666FF;box-shadow:0 2px 8px rgba(102,102,255,0.25)}
.pricing-item.selected.personal .pricing-name{color:#6666FF}
.pricing-item.selected.personal .current-price{color:#6666FF}
.pricing-item.personal:hover:not(.selected){border-color:#6666FF;background:linear-gradient(135deg,rgba(102,102,255,0.08) 0%,rgba(102,102,255,0.12) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(102,102,255,0.2)}
.btn-upgrade.personal{background:linear-gradient(89.95deg,#6666FF 11.5%,#38c0ff 100.01%);border:none;color:white;box-shadow:0 2px 8px rgba(102,102,255,0.3)}
.btn-upgrade.personal:hover{background:linear-gradient(89.95deg,#5555EE 11.5%,#2AAFEE 100.01%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(102,102,255,0.4)}

.version-tab.professional.active{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border-color:#4B4B4B;color:#F9D9A8;box-shadow:0 4px 12px rgba(75,75,75,0.3)}
.version-features-section.professional{background:transparent;border:1px solid rgba(75,75,75,0.03)}
.version-features-section.professional .features-header{background:transparent;border-bottom:none}
.version-features-section.professional .features-title{color:#444444;font-weight:600;font-size:17px}
.version-features-section.professional .features-compare-link{color:#4B4B4B}
.version-features-section.professional .features-compare-link:hover{color:#3A3A3A}
.version-features-section.professional .features-body{background:transparent}
.version-features-section.professional .feature-item:hover{background:rgba(75,75,75,0.04)}
.version-features-section.professional .feature-item i{color:#4B4B4B}
.version-features-section.professional .features-footer{background:transparent;border-top:none}
.version-features-section.professional .expand-btn{border-color:#4B4B4B;color:#4B4B4B;background:rgba(255,255,255,0.9)}
.version-features-section.professional .expand-btn:hover{border-color:#3A3A3A;color:#3A3A3A;background:rgba(255,255,255,1)}
.pricing-item.selected.professional{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border-color:#4B4B4B;color:#F9D9A8;box-shadow:0 2px 8px rgba(75,75,75,0.3)}
.pricing-item.selected.professional .pricing-name{color:#F9D9A8}
.pricing-item.selected.professional .current-price{color:#F9D9A8}
.pricing-item.professional:hover:not(.selected){border-color:#4B4B4B;background:linear-gradient(135deg,rgba(75,75,75,0.08) 0%,rgba(75,75,75,0.12) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(75,75,75,0.2)}
.btn-upgrade.professional{background:linear-gradient(to right,#4B4B4B 5.77%,#1A1510 100%);border:none;color:#F9D9A8;box-shadow:0 2px 8px rgba(75,75,75,0.3)}
.btn-upgrade.professional:hover{background:linear-gradient(to right,#3A3A3A 5.77%,#0F0A06 100%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(75,75,75,0.4)}

.version-tab.flagship.active{background:linear-gradient(to right,#FFEBC1 21.65%,#FFE5B7 79.13%);border-color:#E6D700;color:#944800;box-shadow:0 4px 12px rgba(255,215,0,0.3);position:relative}
.version-tab.flagship.active::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,rgba(255,255,255,0.3) 0%,transparent 50%);border-radius:6px;pointer-events:none}
.version-features-section.flagship{background:transparent;border:1px solid rgba(230,215,0,0.03)}
.version-features-section.flagship .features-header{background:transparent;border-bottom:none}
.version-features-section.flagship .features-title{color:#886622;font-weight:600;font-size:17px}
.version-features-section.flagship .features-compare-link{color:#944800}
.version-features-section.flagship .features-compare-link:hover{color:#7A3600}
.version-features-section.flagship .features-body{background:transparent}
.version-features-section.flagship .feature-item:hover{background:rgba(230,215,0,0.04)}
.version-features-section.flagship .feature-item i{color:#E6D700}
.version-features-section.flagship .features-footer{background:transparent;border-top:none}
.version-features-section.flagship .expand-btn{border-color:#944800;color:#944800;background:rgba(255,255,255,0.9)}
.version-features-section.flagship .expand-btn:hover{border-color:#7A3600;color:#7A3600;background:rgba(255,255,255,1)}
.pricing-item.selected.flagship{background:linear-gradient(to right,#FFEBC1 21.65%,#FFE5B7 79.13%);border-color:#E6D700;color:#944800;box-shadow:0 2px 8px rgba(255,215,0,0.3)}
.pricing-item.selected.flagship .pricing-name{color:#944800}
.pricing-item.selected.flagship .current-price{color:#944800}
.pricing-item.flagship:hover:not(.selected){border-color:#E6D700;background:linear-gradient(135deg,rgba(255,235,193,0.12) 0%,rgba(255,215,0,0.08) 100%);transform:translateY(-2px);box-shadow:0 4px 16px rgba(255,215,0,0.2)}
.btn-upgrade.flagship{background:linear-gradient(to right,#FFEBC1 21.65%,#FFE5B7 79.13%);border:none;color:#944800;font-weight:bold;box-shadow:0 2px 8px rgba(255,215,0,0.3)}
.btn-upgrade.flagship:hover{background:linear-gradient(to right,#FFE5B7 21.65%,#FFDFAB 79.13%);transform:translateY(-1px);box-shadow:0 4px 12px rgba(255,215,0,0.4)}

.btn-upgrade{transition:all 0.3s ease;border-radius:6px;padding:12px 24px;font-size:16px;font-weight:500;position:relative;overflow:hidden}
.btn-upgrade::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);transition:left 0.5s;z-index:1}
.btn-upgrade:hover::before{left:100%}
.version-tab{animation:fadeIn 0.4s ease-out}
.version-tab:nth-child(1){animation-delay:0.05s}
.version-tab:nth-child(2){animation-delay:0.1s}
.version-tab:nth-child(3){animation-delay:0.15s}
.trust-elements{animation:fadeIn 0.8s ease-out 0.5s both}
@keyframes fadeIn{from{opacity:0}to{opacity:1}}
.feature-item{transition:all 0.2s ease}
.feature-item:hover{transform:translateX(4px)}
.compare-content{transition:all 0.3s ease}
.security-badges .badge-item{transition:all 0.2s ease}
.security-badges .badge-item:hover{transform:translateY(-1px);box-shadow:0 2px 8px rgba(40,167,69,0.2)}
.pricing-item{height:120px;padding:16px 20px;border:2px solid #e9ecef;border-radius:16px;text-align:center;cursor:pointer;transition:all 0.3s ease;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);margin:0 0 12px 0;position:relative;overflow:hidden;display:flex;flex-direction:column;justify-content:center;box-shadow:0 4px 12px rgba(0,0,0,0.08)}
.pricing-name{font-size:1rem;font-weight:700;margin-bottom:8px;color:#333}
.pricing-price{display:flex;align-items:center;justify-content:center;gap:8px;margin-bottom:6px;flex-wrap:wrap}
.current-price{font-size:1.5rem;font-weight:bold;color:#333;position:relative}
.original-price{font-size:1rem;color:#999;text-decoration:line-through;opacity:0.8}
.discount-percentage{font-size:0.85rem;color:#ff4757;font-weight:600;background:rgba(255,71,87,0.1);padding:2px 6px;border-radius:12px;margin-left:4px}
.daily-cost{font-size:0.75rem;color:#666;margin-top:2px;font-style:italic}
.pricing-item.selected{box-shadow:0 8px 24px rgba(0,0,0,0.15);border-width:3px}
.pricing-tag{position:absolute;top:-2px;left:12px;padding:4px 12px;border-radius:0 0 12px 12px;font-size:11px;font-weight:bold;z-index:3;color:white;text-shadow:0 1px 2px rgba(0,0,0,0.2)}
.pricing-tag.hot{background:linear-gradient(135deg,#ff6b35 0%,#ff4757 100%);box-shadow:0 3px 8px rgba(255,71,87,0.4)}
.pricing-tag.recommend{background:linear-gradient(135deg,#ff6348 0%,#e55039 100%);box-shadow:0 3px 8px rgba(229,80,57,0.4)}
.pricing-tag.new{background:linear-gradient(135deg,#2ed573 0%,#1dd1a1 100%);box-shadow:0 3px 8px rgba(29,209,161,0.4)}
.discount-badge{position:absolute;top:-2px;right:-2px;background:linear-gradient(135deg,#ff4757 0%,#ff3742 100%);color:white;padding:6px 12px;border-radius:0 12px 0 16px;font-size:13px;font-weight:bold;z-index:2;box-shadow:0 3px 8px rgba(255,71,87,0.4);text-shadow:0 1px 2px rgba(0,0,0,0.2)}
.version-tab .tab-icon{width:32px;height:32px;margin:0 auto 8px;background-size:contain;background-repeat:no-repeat;background-position:center}
.version-tab .tab-name{font-weight:bold;font-size:1rem}

.pricing-section{margin-bottom:30px}
.pricing-options{display:none}
.pricing-options.active{display:grid;grid-template-columns:repeat(3,1fr);gap:16px}
.version-features-section{margin:20px 0;border-radius:12px;transition:border-color 0.3s ease, box-shadow 0.3s ease;background:transparent;border:1px solid rgba(200,200,200,0.4);box-shadow:0 2px 8px rgba(0,0,0,0.05);overflow:hidden}
.features-header{display:flex;justify-content:space-between;align-items:center;padding:16px 16px 12px;margin:0;border-bottom:none;background:transparent}
.features-title{font-size:16px;font-weight:600;margin:0;color:#333}
.features-compare-link{color:#007cfa;text-decoration:none;font-size:14px;transition:color 0.3s ease;font-weight:400;cursor:pointer}
.features-compare-link:hover{color:#0056b3;text-decoration:none}

.compare-modal{display:none;position:fixed;z-index:1000;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5)}
.compare-modal-content{background-color:#fefefe;margin:1% auto;padding:0;border-radius:16px;width:90%;max-width:1400px;height:95vh;overflow:hidden;box-shadow:0 12px 48px rgba(0,0,0,0.15);display:flex;flex-direction:column}
.compare-modal-header{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);padding:14px 32px;border-bottom:1px solid #dee2e6;display:flex;justify-content:space-between;align-items:center;flex-shrink:0;border-radius:16px 16px 0 0}
.compare-modal-title{font-size:22px;font-weight:600;color:#2c3e50;margin:0;display:flex;align-items:center}
.compare-modal-title::before{content:"📊";margin-right:8px;font-size:20px}
.compare-modal-close{color:#6c757d;font-size:32px;font-weight:bold;cursor:pointer;line-height:1;padding:8px;border-radius:50%;transition:all 0.3s ease;display:flex;align-items:center;justify-content:center;width:48px;height:48px}
.compare-modal-close:hover{color:#dc3545;background-color:rgba(220,53,69,0.1);transform:scale(1.1)}
.compare-modal-body{padding:0;flex:1;overflow:hidden;border-radius:0 0 16px 16px}
.compare-modal-body iframe{width:100%;height:100%;border:none;border-radius:0 0 16px 16px}

@media (max-width:1200px){.compare-modal-content{width:95%;max-width:none}}
@media (max-width:768px){.compare-modal-content{width:98%;height:98vh;margin:1% auto;border-radius:12px}.compare-modal-header{padding:16px 20px;border-radius:12px 12px 0 0}.compare-modal-title{font-size:18px}.compare-modal-close{width:40px;height:40px;font-size:28px}.compare-modal-body iframe{border-radius:0 0 12px 12px}}
.features-body{padding:8px 16px 12px;background:transparent}
.features-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:3px 8px;margin:0}
.feature-item{display:flex;align-items:flex-start;padding:6px 0;background:transparent;border:none;font-size:14px;transition:background-color 0.2s ease}
.feature-item:hover{background:rgba(255,255,255,0.4);border-radius:4px}
.feature-item i{color:#28a745;margin-right:6px;font-size:14px;margin-top:1px;flex-shrink:0}
.feature-item .feature-content{flex:1}
.feature-item .feature-name{font-weight:500;color:#333;margin-bottom:1px;line-height:1.2;font-size:14px}
.feature-item .feature-desc{color:#666;font-size:12px;line-height:1.2}

.features-footer{padding:8px 16px 16px;background:transparent;border-top:none;text-align:center}
.expand-btn{background:rgba(255,255,255,0.9);border:1px solid #007cfa;color:#007cfa;font-size:13px;cursor:pointer;display:inline-flex;align-items:center;justify-content:center;transition:all 0.2s ease;padding:6px 14px;border-radius:14px;font-weight:400;box-shadow:0 1px 3px rgba(0,0,0,0.08)}
.expand-btn:hover{color:#0056b3;background:rgba(255,255,255,1);border-color:#0056b3;box-shadow:0 2px 6px rgba(0,0,0,0.12);transform:translateY(-1px)}
.expand-btn i{margin-left:4px;transition:transform 0.3s ease;font-size:12px}
.expand-btn.expanded i{transform:rotate(180deg)}



.order-summary{border:1px solid rgba(0,123,250,0.1);border-radius:16px;padding:24px;margin-bottom:24px;background:linear-gradient(135deg,#ffffff 0%,#fafbfc 100%);box-shadow:0 4px 16px rgba(0,0,0,0.06)}
.summary-item{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;padding:8px 0}
.summary-label{color:#666;font-size:15px;font-weight:500}
.summary-value{font-weight:600;color:#333;font-size:15px}
.summary-item.discount .summary-value{color:#ff4757}
.summary-divider{height:2px;background:linear-gradient(90deg,transparent,#e9ecef,transparent);margin:20px 0}
.summary-total{display:flex;justify-content:space-between;align-items:center;margin-top:20px;padding-top:20px;border-top:2px solid #007cfa}
.total-label{font-size:1.1rem;font-weight:bold;color:#333}
.total-price{font-size:1.5rem;font-weight:bold;color:#007cfa}
.order-total{padding:20px 0;border-top:2px solid rgba(0,123,250,0.1);background:linear-gradient(135deg,rgba(0,123,250,0.02) 0%,rgba(0,123,250,0.05) 100%);border-radius:12px;margin:-8px -8px 0 -8px;padding:20px 16px}
.total-row{display:flex;justify-content:space-between;align-items:center}
.total-prices{text-align:right;display:flex;flex-direction:column;align-items:flex-end;gap:4px}
.discount-info{color:#ff6600;font-size:13px;font-weight:600;background:linear-gradient(135deg,rgba(255,102,0,0.1) 0%,rgba(255,102,0,0.15) 100%);padding:4px 10px;border-radius:16px;border:1px solid rgba(255,102,0,0.2);animation:pulse-discount 2s infinite}
@keyframes pulse-discount{0%,100%{transform:scale(1)}50%{transform:scale(1.02)}}
@keyframes pulse{0%,100%{opacity:1}50%{opacity:0.6}}
.urgency-notice{animation:glow 3s ease-in-out infinite alternate}
@keyframes glow{from{box-shadow:0 0 5px rgba(255,102,0,0.3)}to{box-shadow:0 0 15px rgba(255,102,0,0.5)}}
.original-total{color:#999;font-size:18px;text-decoration:line-through;opacity:0.8;font-weight:600}
.current-total{color:#007cfa;font-size:40px;font-weight:900;text-shadow:0 2px 6px rgba(0,123,250,0.3);line-height:1;animation:price-pulse 3s ease-in-out infinite}
@keyframes price-pulse{0%,100%{transform:scale(1)}50%{transform:scale(1.02)}}
.trust-elements{margin-bottom:30px;padding:24px;background:linear-gradient(135deg,#f8f9fa 0%,#ffffff 100%);border-radius:16px;border:1px solid rgba(0,123,250,0.08);box-shadow:0 4px 16px rgba(0,0,0,0.06)}
.social-proof{margin-bottom:20px;text-align:center}
.user-stats{display:flex;align-items:center;justify-content:center;margin-bottom:12px;font-size:15px;color:#333;font-weight:500}
.user-stats i{color:#007cfa;margin-right:10px;font-size:18px;animation:pulse 2s infinite}
.user-stats strong{color:#007cfa;font-size:16px;font-weight:700}
.user-count{display:inline-block;min-width:60px;text-align:center}
.rating-info{display:flex;align-items:center;justify-content:center;gap:10px;font-size:14px;color:#666}
.stars{display:flex;gap:3px}
.stars i{color:#ffc107;font-size:16px;transition:transform 0.2s ease}
.stars i:hover{transform:scale(1.1)}
.rating-text{font-weight:500}
.security-badges{display:grid;grid-template-columns:1fr;gap:10px;margin-top:16px}
.badge-item{display:flex;align-items:center;padding:12px 16px;background:linear-gradient(135deg,rgba(40,167,69,0.08) 0%,rgba(40,167,69,0.12) 100%);border-radius:12px;font-size:13px;color:#28a745;border:1px solid rgba(40,167,69,0.15);transition:all 0.3s ease;font-weight:500}
.badge-item:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(40,167,69,0.2);background:linear-gradient(135deg,rgba(40,167,69,0.12) 0%,rgba(40,167,69,0.16) 100%)}
.badge-item i{margin-right:8px;font-size:16px}
@keyframes pulse{0%,100%{opacity:1}50%{opacity:0.7}}
@keyframes shine{0%{left:-100%}100%{left:100%}}
.right-section{animation:slideInRight 0.6s ease-out}
@keyframes slideInRight{from{opacity:0;transform:translateX(30px)}to{opacity:1;transform:translateX(0)}}
.order-summary{animation:fadeInUp 0.8s ease-out 0.2s both}
@keyframes fadeInUp{from{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}
.summary-item{animation:fadeIn 0.6s ease-out both}
.summary-item:nth-child(1){animation-delay:0.1s}
.summary-item:nth-child(2){animation-delay:0.2s}
.summary-item:nth-child(3){animation-delay:0.3s}
.order-total{animation:fadeIn 0.6s ease-out 0.4s both}
.account-input{margin:24px 0}
.account-input .form-control{width:100%;padding:16px;border:2px solid #e9ecef;border-radius:12px;font-size:15px;transition:all 0.3s ease;background:rgba(255,255,255,0.8)}
.account-input .form-control:focus{border-color:#007cfa;box-shadow:0 0 0 3px rgba(0,123,250,0.1);outline:none;background:white}
.account-input .form-control::placeholder{color:#999;font-weight:400}
.btn-upgrade{width:100%;padding:18px;background:linear-gradient(135deg,#007cfa 0%,#0056b3 100%);color:white;border:none;border-radius:12px;font-size:16px;font-weight:600;cursor:pointer;transition:all 0.3s ease;position:relative;overflow:hidden;box-shadow:0 4px 16px rgba(0,123,250,0.3);text-transform:uppercase;letter-spacing:0.5px}
.btn-upgrade:hover{background:linear-gradient(135deg,#0056b3 0%,#004494 100%);transform:translateY(-2px);box-shadow:0 6px 20px rgba(0,123,250,0.4)}
.btn-upgrade:active{transform:translateY(0);box-shadow:0 2px 8px rgba(0,123,250,0.3)}
.btn-upgrade::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);transition:left 0.5s;z-index:1}
.btn-upgrade:hover::before{left:100%}
.btn-upgrade.loading{pointer-events:none;opacity:0.8}
.btn-upgrade.loading::after{content:'';position:absolute;top:50%;left:50%;width:20px;height:20px;margin:-10px 0 0 -10px;border:2px solid transparent;border-top:2px solid white;border-radius:50%;animation:spin 1s linear infinite;z-index:2}
@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
.icon-v-1{background:url(/static/image/vip_1.png) 0 center no-repeat;background-size:contain}
.icon-v1{background:url(/static/image/vip_2.png) 0 center no-repeat;background-size:contain}
.icon-v3{background:url(/static/image/vip_3.png) 0 center no-repeat;background-size:contain}
@media (max-width:768px){
.upgrade-container{padding:15px 10px;padding-top:60px}
.main-content{flex-direction:column;gap:20px}
.left-section,.right-section{padding:20px 16px;border-radius:8px}
.right-section{position:static;margin-top:20px}
.version-tabs{flex-direction:column;gap:12px}
.version-tab{min-width:auto;padding:12px 16px 16px;margin-bottom:0}
.version-tab .tab-icon{width:28px;height:28px;margin-bottom:6px}
.version-tab .tab-name{font-size:0.95rem}
.version-tab .version-audience{font-size:0.75rem}
.version-recommend-badge{padding:3px 8px;font-size:10px}
.pricing-options{grid-template-columns:1fr;gap:12px}
.pricing-item{height:auto;min-height:100px;padding:14px 16px}
.pricing-name{font-size:0.95rem}
.current-price{font-size:1.3rem}
.original-price{font-size:0.9rem}
.discount-percentage{font-size:0.8rem}
.daily-cost{font-size:0.7rem}
.discount-badge{padding:4px 8px;font-size:11px}
.features-grid{grid-template-columns:1fr;gap:6px}
.version-features-section{margin:15px 0;border-radius:8px}
.features-header{padding:12px 16px}
.features-body{padding:8px 16px 12px}
.features-footer{padding:8px 16px 12px}
.feature-item{padding:8px 0;font-size:14px}
.feature-item .feature-name{font-size:14px}
.feature-item .feature-desc{font-size:12px}
.expand-btn{padding:8px 16px;font-size:13px}
.trust-elements{padding:20px;margin-bottom:24px;border-radius:12px}
.user-stats{font-size:14px}
.user-stats i{font-size:16px}
.rating-info{font-size:13px}
.stars i{font-size:15px}
.security-badges{grid-template-columns:1fr;gap:8px}
.badge-item{padding:10px 12px;font-size:12px}
.badge-item i{font-size:14px}

.order-summary{padding:20px;border-radius:12px}
.summary-item{margin-bottom:14px}
.summary-label{font-size:14px}
.summary-value{font-size:14px}
.order-total{padding:16px 12px;margin:-6px -6px 0 -6px}
.current-total{font-size:20px}
.discount-info{font-size:12px}
.original-total{font-size:13px}
.account-input .form-control{padding:14px;font-size:14px}
.btn-upgrade{padding:16px;font-size:15px}
.user-testimonials{height:58px}
.testimonial-item{height:50px;padding:10px;margin-bottom:6px}
.testimonial-item div:first-child{font-size:12px}
.testimonial-item div:last-child{font-size:10px}
.security-badges div{font-size:11px;padding:4px 8px;margin:2px}
.service-guarantee{margin:12px 0;padding:10px 12px}
.service-guarantee div:first-child{font-size:13px;margin-bottom:6px}
.service-guarantee div:last-child div{font-size:11px;padding:3px 6px}
.account-section{min-height:50px}
.account-info{padding:12px;font-size:12px}
.payment-methods{margin:12px 0}
.payment-methods div:first-child{font-size:11px}
.payment-methods i{font-size:20px}
.help-links{margin-top:12px;font-size:11px}
.current-total{font-size:36px}
.original-total{font-size:12px}
.discount-info{font-size:10px;padding:1px 6px}
}
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div class="upgrade-container">
        <div class="main-content">
            <div class="left-section">
                <div class="section-title">
                    选择升级类型
                </div>

                <div class="version-tabs" id="versionTabs">
                    <%
                        var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();

                        // 检查用户登录状态
                        var currentUser = Account.Web.Common.AuthHelper.GetUserSession(Request);
                        var isLoggedIn = currentUser != null && !string.IsNullOrEmpty(currentUser.Account);
                        var currentAccount = isLoggedIn ? currentUser.Account : "";

                        int tabIndex = 0;
                        foreach (var userType in lstUserTypes)
                        {
                            var typeHash = userType.Type.GetHashCode();
                            var isActive = tabIndex == 0 ? "active" : "";
                            var iconClass = "icon-v" + typeHash;

                            // 根据版本类型设置描述、CSS类、推荐标签和适用人群
                            string typeDesc = "";
                            string versionClass = "";
                            string recommendBadge = "";
                            string targetAudience = "";

                            switch (userType.Type.ToString())
                            {
                                case "个人版":
                                    typeDesc = "个人专属，轻松识别";
                                    versionClass = "personal";
                                    recommendBadge = "最多选择";
                                    targetAudience = "";
                                    break;
                                case "专业版":
                                    typeDesc = "专业高效，精确稳定";
                                    versionClass = "professional";
                                    recommendBadge = "推荐";
                                    targetAudience = "";
                                    break;
                                case "旗舰版":
                                    typeDesc = "旗舰体验，最佳选择";
                                    versionClass = "flagship";
                                    recommendBadge = "热门";
                                    targetAudience = "";
                                    break;
                                default:
                                    typeDesc = "专业服务，品质保证";
                                    versionClass = "professional";
                                    recommendBadge = "";
                                    targetAudience = "";
                                    break;
                            }
                    %>
                    <div class="version-tab <%=versionClass %> <%=isActive %>" data-type="<%=typeHash %>" data-desc="<%=typeDesc %>" onclick="selectVersion('<%=typeHash %>')">
                        <%if (!string.IsNullOrEmpty(recommendBadge)) { %>
                        <div class="version-recommend-badge"><%=recommendBadge %></div>
                        <%} %>
                        <div class="tab-icon <%=iconClass %>"></div>
                        <div class="tab-name"><%=userType.Type.ToString() %></div>
                    </div>
                    <%
                            tabIndex++;
                        }
                    %>
                </div>

                <!-- 版本专属功能区域 -->
                <div class="version-features-section" id="versionFeaturesSection">
                    <!-- 标题栏 -->
                    <div class="features-header">
                        <h3 class="features-title" id="featuresTitle">专属功能</h3>
                        <a href="javascript:void(0);" class="features-compare-link" onclick="openCompareModal()"><i class="fa fa-info-circle"></i>&nbsp;功能比对 ></a>
                    </div>

                    <!-- 内容区域 -->
                    <div class="features-body">
                        <div class="features-grid" id="featuresGrid">
                            <!-- 功能项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 底部区域 -->
                    <div class="features-footer" id="featuresExpand" style="display: none;">
                        <button type="button" class="expand-btn" onclick="return toggleFeatures(event);">
                            <span id="expandText">展开</span>
                            <i class="fa fa-angle-down" id="expandIcon"></i>
                        </button>
                    </div>
                </div>



                <div class="section-title">选择订阅方式</div>
                
                <div class="pricing-section" id="pricingSection">
                    <%
                        tabIndex = 0;
                        foreach (var userType in lstUserTypes)
                        {
                            var typeHash = userType.Type.GetHashCode();
                            var isActive = tabIndex == 0 ? "active" : "";

                            // 构建价格选项列表
                            var lstChargeType = new List<ChargeViewToUser>();
                            try
                            {
                                if (userType.ChargeTypes != null)
                                {
                                    foreach (var q in userType.ChargeTypes)
                                    {
                                        string strDesc = "";
                                        var price = q.GetPrice(userType.PerPrice, ref strDesc);
                                        var charge = new ChargeViewToUser()
                                        {
                                            Name = q.Name,
                                            Desc = strDesc,
                                            Price = (double)price,
                                            OriPrice = (double)q.OriPrice,
                                            IsDefault = q.IsDefault,
                                            Tag = q.Tag,
                                        };
                                        lstChargeType.Add(charge);
                                    }
                                }
                            }
                            catch
                            {
                                // 如果价格计算失败，使用默认价格
                                lstChargeType.Add(new ChargeViewToUser
                                {
                                    Name = "一年",
                                    Desc = "",
                                    Price = 100,
                                    OriPrice = 100,
                                    IsDefault = true,
                                    Tag = ""
                                });
                            }
                    %>
                    <div class="pricing-options <%=isActive %>" data-type="<%=typeHash %>">
                        <%
                            int priceIndex = 0;
                            foreach (var charge in lstChargeType)
                            {
                                var isSelected = charge.IsDefault ? "selected" : "";
                                var tagClass = "";
                                var tagText = "";

                                switch (charge.Tag != null ? charge.Tag.ToLower() : null)
                                {
                                    case "hot":
                                        tagClass = "hot";
                                        tagText = "热门";
                                        break;
                                    case "_new":
                                        tagClass = "new";
                                        tagText = "新";
                                        break;
                                    case "recommond":
                                        tagClass = "recommend";
                                        tagText = "推荐";
                                        break;
                                }

                                // 计算折扣率（几折）
                                var discountRate = charge.OriPrice > 0 && charge.OriPrice > charge.Price ? Math.Round((charge.Price / charge.OriPrice) * 10, 1) : 0;
                        %>
                        <div class="pricing-item <%=isSelected %>" data-name="<%=charge.Name %>" data-price="<%=charge.Price %>" data-original="<%=charge.OriPrice %>" data-desc="<%=charge.Desc %>" data-is-default="<%=charge.IsDefault.ToString().ToLower() %>" onclick="selectPricing(this)">
                            <%if (!string.IsNullOrEmpty(tagText)) { %>
                            <div class="pricing-tag <%=tagClass %>"><%=tagText %></div>
                            <%} %>
                            <%if (charge.OriPrice > charge.Price) {
                                // 正确的折扣计算：现价/原价*10，保留一位小数
                                var discountPercent = Math.Round((charge.Price / charge.OriPrice) * 10, 1);
                            %>
                            <div class="discount-badge"><%=discountPercent %>折</div>
                            <%} %>
                            <div class="pricing-name"><%=charge.Name %></div>
                            <div class="pricing-price">
                                <span class="current-price">¥<%=charge.Price.ToString("F0") %></span>
                                <%if (charge.OriPrice > charge.Price) { %>
                                <span class="original-price">¥<%=charge.OriPrice.ToString("F0") %></span>
                                <span class="discount-percentage">省¥<%=(charge.OriPrice - charge.Price).ToString("F0") %></span>
                                <%} %>
                            </div>
                            <%
                                // 计算每日成本（假设一年365天）
                                var dailyCost = charge.Price / 365;
                                if (charge.Name.Contains("终身")) {
                                    dailyCost = charge.Price / (365 * 10); // 终身按10年计算
                                }
                            %>
                            <div class="daily-cost">每天仅需¥<%=dailyCost.ToString("F2") %></div>
                        </div>
                        <%
                                priceIndex++;
                            }
                        %>
                    </div>
                    <%
                            tabIndex++;
                        }
                    %>
                </div>
            </div>

            <div class="right-section">
                <!-- 信任要素区域 -->
                <div class="trust-elements">
                    <div class="social-proof">
                        <div class="user-stats">
                            <i class="fa fa-users"></i>
                            <span>已有 <strong><span class="user-count">50,000+</span></strong> 用户选择升级</span>
                        </div>
                        <div class="rating-info">
                            <div class="stars">
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                                <i class="fa fa-star"></i>
                            </div>
                            <span class="rating-text">4.8分 (2,341条评价)</span>
                        </div>

                        <div class="user-testimonials" style="margin-top:16px;height:68px;overflow:hidden;position:relative;">
                            <div class="testimonial-slider" id="testimonialSlider" style="transition:transform 0.5s ease;">
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "识别准确率很高，大大提升了工作效率！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自北京的李**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "批量处理文档太方便了，节省了大量时间！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自上海的王**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "客服响应很快，技术支持很专业！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自广州的张**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "表格识别功能太强大了，完全解放双手！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自深圳的陈**用户
                                    </div>
                                </div>
                                <div class="testimonial-item" style="height:60px;padding:12px;background:rgba(0,123,250,0.05);border-radius:8px;border-left:3px solid #007cfa;margin-bottom:8px;display:flex;flex-direction:column;justify-content:center;">
                                    <div style="font-size:13px;color:#333;line-height:1.4;margin-bottom:6px;">
                                        "多设备同步使用，办公效率翻倍！"
                                    </div>
                                    <div style="font-size:11px;color:#666;text-align:right;">
                                        —— 来自杭州的刘**用户
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:16px;">
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-certificate" style="margin-right:4px;font-size:11px;"></i>正版授权
                        </div>
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-cloud-upload-alt" style="margin-right:4px;font-size:11px;"></i>云端同步
                        </div>
                        <div style="display:flex;align-items:center;color:#007cfa;font-size:12px;background:rgba(0,123,250,0.08);padding:6px 10px;border-radius:12px;border:1px solid rgba(0,123,250,0.15);">
                            <i class="fa fa-users-cog" style="margin-right:4px;font-size:11px;"></i>专业团队
                        </div>
                    </div>
                </div>

                <div class="section-title" style="color:#007cfa;font-size:1.4rem;margin-bottom:24px;text-align:left;position:relative;display:flex;align-items:center;justify-content:flex-start;">
                    <i class="fa fa-crown" style="margin-right:8px;color:#ffc107;"></i>
                    <span style="margin:0;">您的升级方案</span>
                </div>

                <%
                    // 定义默认值变量
                    var firstUserType = lstUserTypes.FirstOrDefault();
                    var defaultCharge = firstUserType != null && firstUserType.ChargeTypes != null ? firstUserType.ChargeTypes.FirstOrDefault(c => c.IsDefault) : null;
                    string defaultVersionName = firstUserType != null ? firstUserType.Type.ToString() : "";
                    string defaultPlanName = defaultCharge != null ? defaultCharge.Name : "一年";
                    string defaultDesc = defaultCharge != null ? defaultCharge.Desc : "";
                    decimal defaultOriPrice = defaultCharge != null ? defaultCharge.OriPrice : 100;
                    decimal defaultCurrentPrice = 100;
                    decimal defaultDiscount = 0;

                    if (defaultCharge != null && firstUserType != null)
                    {
                        string tempDesc = "";
                        defaultCurrentPrice = defaultCharge.GetPrice(firstUserType.PerPrice, ref tempDesc);
                        defaultDiscount = defaultOriPrice - defaultCurrentPrice;
                    }
                %>

                <div class="order-summary" id="orderSummary">
                    <div class="summary-item">
                        <div class="summary-label">选择方案</div>
                        <div class="summary-value" id="selectedPlan"><%=defaultPlanName + defaultVersionName %></div>
                    </div>

                    <div class="summary-item">
                        <div class="summary-label">服务期限</div>
                        <div class="summary-value" id="selectedDesc"><%=defaultDesc %></div>
                    </div>



                    <div class="summary-divider"></div>

                    <div class="order-total" style="background:linear-gradient(135deg,rgba(0,123,250,0.05) 0%,rgba(0,123,250,0.08) 100%);border:2px solid rgba(0,123,250,0.15);border-radius:16px;padding:10px 20px 10px 20px;margin:16px 0;position:relative;">
                        <div style="position:absolute;top:-8px;right:-2px;background:linear-gradient(135deg,#ff6600,#ff8533);color:white;padding:6px 12px;border-radius:12px;font-size:13px;font-weight:700;box-shadow:0 2px 8px rgba(255,102,0,0.3);border:1px solid rgba(255,255,255,0.2);" id="discountAmount">
                            <i class="fa fa-clock" style="margin-right:4px;animation:pulse 2s infinite;"></i>限时节省¥<%=(defaultOriPrice - defaultCurrentPrice).ToString("F0") %>
                        </div>

                        <div class="total-row" style="justify-content:center;">
                            <div class="total-prices">
                                <div style="display:flex;align-items:baseline;gap:16px;justify-content:center;">
                                    <span class="current-total" style="font-size:40px;font-weight:900;color:#007cfa;text-shadow:0 2px 6px rgba(0,123,250,0.3);line-height:1;">¥<%=defaultCurrentPrice.ToString("F0") %></span>
                                    <span class="original-total" style="font-size:18px;color:#999;text-decoration:line-through;opacity:0.8;font-weight:600;">¥<%=defaultOriPrice.ToString("F0") %></span>
                                </div>
                            </div>
                        </div>
                    </div>

                        <%if (!isLoggedIn) { %>
                    <div class="account-section" style="min-height:60px;margin:16px 0;">
                        <div class="account-input" id="accountInput">
                            <input type="text" id="txtAccount" placeholder="请输入您的账号（手机号/邮箱）" class="form-control" value="<%=currentAccount %>" />
                        </div>
                    </div>
                        <%} %>

                    <button type="button" class="btn-upgrade" id="upgradeBtn" onclick="submitUpgrade()" style="position:relative;overflow:hidden;margin:10px 0 12px 0;">
                        <span class="btn-text">立即升级</span>
                        <div class="btn-shine" style="position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.3),transparent);animation:shine 2s infinite;"></div>
                    </button>

                    <div class="service-guarantee" style="background:rgba(40,167,69,0.08);margin:10px 0;padding:16px;border-radius:12px;border-left:4px solid #28a745;">
                        <div style="color:#28a745;font-weight:600;font-size:14px;margin-bottom:12px;text-align:left;">
                            <i class="fa fa-shield-check" style="margin-right:6px;"></i>服务保障
                        </div>
                        <div style="display:flex;justify-content:center;flex-wrap:wrap;gap:8px;">
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-bolt" style="margin-right:4px;font-size:10px;"></i>立即生效
                            </div>
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-undo" style="margin-right:4px;font-size:10px;"></i>7天退款
                            </div>
                            <div style="display:flex;align-items:center;color:#28a745;font-size:11px;background:rgba(40,167,69,0.1);padding:4px 8px;border-radius:12px;">
                                <i class="fa fa-headset" style="margin-right:4px;font-size:10px;"></i>24h客服
                            </div>
                        </div>
                    </div>


                </div>


            </div>
        </div>
    </div>

    <!-- 功能对比弹框 -->
    <div id="compareModal" class="compare-modal">
        <div class="compare-modal-content">
            <div class="compare-modal-header">
                <h2 class="compare-modal-title">各版本功能比较</h2>
                <span class="compare-modal-close" onclick="closeCompareModal()">&times;</span>
            </div>
            <div class="compare-modal-body">
                <iframe id="compareIframe" src="desc.aspx" frameborder="0"></iframe>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        var currentAccount = '';
        var phoneReg = /^1[3456789]\d{9}$/;
        var emailReg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;

var versionCoreFeatures={'个人版':[{name:'基础识别套装',desc:'本地识别+图片识别+区域识别+竖排识别'},{name:'翻译功能',desc:'划词翻译+图片翻译'},{name:'使用限制',desc:'每日200次，1秒/次'},{name:'设备授权',desc:'最多2台设备同时使用'},{name:'专属客服',desc:'优先技术支持'},{name:'需求定制',desc:'个性化功能定制'}],'专业版':[{name:'基础识别套装',desc:'包含个人版全部识别功能'},{name:'高级识别',desc:'公式识别+表格识别'},{name:'翻译功能',desc:'划词翻译+图片翻译'},{name:'性能提升',desc:'每日500次，1秒/2次'},{name:'多设备支持',desc:'最多3台设备同时使用'},{name:'专属客服',desc:'优先技术支持+需求定制'}],'旗舰版':[{name:'完整识别套装',desc:'包含专业版全部功能'},{name:'文档处理',desc:'文档识别+文档翻译+批量识别'},{name:'多结果显示',desc:'多种识别结果对比'},{name:'极速体验',desc:'每日2000次，1秒/3次'},{name:'多设备授权',desc:'最多5台设备同时使用'},{name:'自选通道',desc:'多种识别引擎可选择'}]};



var isExpanded=false;
function initCurrentVersionFeatures(){updateCurrentVersionFeatures()}
function updateCurrentVersionFeatures(){var selectedTab=document.querySelector('.version-tab.active');var typeName=selectedTab.querySelector('.tab-name').textContent;var versionClass=getVersionClass(selectedTab);var typeDesc=selectedTab.getAttribute('data-desc');document.getElementById('featuresTitle').textContent=typeDesc;var featuresSection=document.getElementById('versionFeaturesSection');featuresSection.className=featuresSection.className.replace(/\b(personal|professional|flagship)\b/g,'')+' '+versionClass;var features=versionCoreFeatures[typeName]||[];var featuresHtml='';var displayCount=isExpanded?features.length:Math.min(4,features.length);for(var i=0;i<displayCount;i++){var feature=features[i];featuresHtml+='<div class="feature-item"><i class="fa fa-check"></i><div class="feature-content"><div class="feature-name">'+feature.name+'</div>';if(feature.desc)featuresHtml+='<div class="feature-desc">'+feature.desc+'</div>';featuresHtml+='</div></div>'}document.getElementById('featuresGrid').innerHTML=featuresHtml;var expandSection=document.getElementById('featuresExpand');if(features.length>4){expandSection.style.display='block';document.getElementById('expandText').textContent=isExpanded?'收起':'展开';var icon=document.getElementById('expandIcon');icon.className=isExpanded?'fa fa-angle-up':'fa fa-angle-down';document.querySelector('.expand-btn').classList.toggle('expanded',isExpanded)}else{expandSection.style.display='none'}}
function toggleFeatures(event){if(event){event.preventDefault();event.stopPropagation()}isExpanded=!isExpanded;updateCurrentVersionFeatures();return false}
function getVersionClass(tab){if(tab.classList.contains('personal'))return 'personal';if(tab.classList.contains('professional'))return 'professional';if(tab.classList.contains('flagship'))return 'flagship';return 'professional'}
document.addEventListener('DOMContentLoaded',function(){initCurrentVersionFeatures();var selectedTab=document.querySelector('.version-tab.active');updateVersionTheme(selectedTab);updateOrderSummary();initFormValidation();animateUserCount();startTestimonialSlider()});function initFormValidation(){var accountInput=document.getElementById('txtAccount');if(accountInput){accountInput.addEventListener('input',function(){var account=this.value.trim();var isValid=phoneReg.test(account)||emailReg.test(account);this.style.borderColor=account===''?'#e9ecef':isValid?'#28a745':'#dc3545';if(account!==''&&!isValid){this.style.boxShadow='0 0 0 3px rgba(220,53,69,0.1)'}else if(isValid){this.style.boxShadow='0 0 0 3px rgba(40,167,69,0.1)'}else{this.style.boxShadow='none'}});accountInput.addEventListener('blur',function(){if(this.style.borderColor==='rgb(220, 53, 69)'){this.style.boxShadow='0 0 0 3px rgba(220,53,69,0.1)'}})}}function animateUserCount(){var userCountElement=document.querySelector('.user-count');if(userCountElement){var targetNumber=50000;var currentNumber=0;var increment=targetNumber/100;var timer=setInterval(function(){currentNumber+=increment;if(currentNumber>=targetNumber){currentNumber=targetNumber;clearInterval(timer)}userCountElement.textContent=Math.floor(currentNumber).toLocaleString()+'+'},20)}}function startTestimonialSlider(){var slider=document.getElementById('testimonialSlider');var items=slider.querySelectorAll('.testimonial-item');if(items.length===0)return;var currentIndex=0;var containerHeight=60;var itemHeight=containerHeight+8;setInterval(function(){currentIndex=(currentIndex+1)%items.length;slider.style.transform='translateY(-'+currentIndex*itemHeight+'px)'},3000)}
function checkUserLogin(){fetch("User.ashx?op=getUserInfo").then(response=>response.json()).then(result=>{if(result.success&&result.data&&result.data.account){currentAccount=result.data.account;document.getElementById('accountInput').style.display='none'}else{currentAccount='';document.getElementById('accountInput').style.display='block'}}).catch(()=>{currentAccount='';document.getElementById('accountInput').style.display='block'})}

function selectVersion(typeHash){
// 切换版本标签
document.querySelectorAll('.version-tab').forEach(tab=>tab.classList.remove('active'));
var selectedTab=document.querySelector('.version-tab[data-type="'+typeHash+'"]');
selectedTab.classList.add('active');

// 切换价格选项区域
document.querySelectorAll('.pricing-options').forEach(option=>option.classList.remove('active'));
var targetPricingOptions=document.querySelector('.pricing-options[data-type="'+typeHash+'"]');
targetPricingOptions.classList.add('active');

// 清除所有价格项的选中状态
document.querySelectorAll('.pricing-item').forEach(item=>item.classList.remove('selected'));

// 重新选择当前版本的默认项
var defaultPricing=null;

// 首先查找标记为IsDefault=true的项
defaultPricing=targetPricingOptions.querySelector('.pricing-item[data-is-default="true"]');

// 如果没有找到IsDefault的项，选择第一个
if(!defaultPricing){
defaultPricing=targetPricingOptions.querySelector('.pricing-item');
}

// 设置选中状态
if(defaultPricing){
defaultPricing.classList.add('selected');
}

updateVersionTheme(selectedTab);
updateOrderSummary();
updateCurrentVersionFeatures();
}
function updateVersionTheme(selectedTab){var versionClass=getVersionClass(selectedTab);document.querySelectorAll('.pricing-item').forEach(item=>{item.classList.remove('personal','professional','flagship');item.classList.add(versionClass)});document.querySelectorAll('.btn-upgrade').forEach(btn=>{btn.className=btn.className.replace(/\b(personal|professional|flagship)\b/g,'')+' '+versionClass});var featuresSection=document.getElementById('versionFeaturesSection');featuresSection.className=featuresSection.className.replace(/\b(personal|professional|flagship)\b/g,'')+' '+versionClass}
function selectPricing(element){document.querySelectorAll('.pricing-item').forEach(item=>item.classList.remove('selected'));element.classList.add('selected');var selectedTab=document.querySelector('.version-tab.active');var versionClass=getVersionClass(selectedTab);document.querySelectorAll('.pricing-item').forEach(item=>{item.classList.remove('personal','professional','flagship');item.classList.add(versionClass)});updateOrderSummary()}
function updateOrderSummary(){var selectedVersion=document.querySelector('.version-tab.active .tab-name').textContent;var selectedPricing=document.querySelector('.pricing-item.selected');if(selectedPricing){var planName=selectedPricing.getAttribute('data-name');var price=parseFloat(selectedPricing.getAttribute('data-price'));var originalPrice=parseFloat(selectedPricing.getAttribute('data-original'));var desc=selectedPricing.getAttribute('data-desc');var discount=(originalPrice-price).toFixed(0);document.getElementById('selectedPlan').textContent=planName+selectedVersion;document.getElementById('selectedDesc').textContent=desc||'';var discountInfo=document.querySelector('.discount-info');var originalTotal=document.querySelector('.original-total');var currentTotal=document.querySelector('.current-total');var discountAmount=document.getElementById('discountAmount');if(discountInfo)discountInfo.textContent='已优惠: ¥'+discount;if(originalTotal)originalTotal.textContent='¥'+originalPrice.toFixed(0);if(currentTotal)currentTotal.textContent='¥'+price.toFixed(0);if(discountAmount)discountAmount.innerHTML='<i class="fa fa-clock" style="margin-right:4px;animation:pulse 2s infinite;"></i>限时节省¥'+discount}}
function submitUpgrade(){var selectedVersion=document.querySelector('.version-tab.active .tab-name').textContent;var selectedPricing=document.querySelector('.pricing-item.selected');if(!selectedPricing){alert('请选择订阅方式！');return}var accountInput=document.getElementById('txtAccount');var account=currentAccount||(accountInput?accountInput.value.trim():'');if(!account){var accountSection=document.getElementById('accountInput');if(accountSection)accountSection.style.display='block';alert('请输入您要升级的账号（手机号/邮箱）！');if(accountInput)accountInput.focus();return}if(!phoneReg.test(account)&&!emailReg.test(account)){alert("账号格式有误（手机号/邮箱），请检查后重试！");if(accountInput)accountInput.focus();return}var upgradeBtn=document.getElementById('upgradeBtn');var btnText=upgradeBtn.querySelector('.btn-text');upgradeBtn.classList.add('loading');btnText.textContent='处理中...';upgradeBtn.disabled=true;var planName=selectedPricing.getAttribute('data-name');var upgradeType=planName+selectedVersion;fetch("code.aspx?op=pay&remark="+encodeURIComponent(upgradeType)+"&account="+encodeURIComponent(account)).then(response=>response.text()).then(result=>{if(result.indexOf('http')!==-1){window.location.href=result.replace("http:","").replace("https:","")}else{alert(result);resetUpgradeButton()}}).catch(()=>{alert('请求失败，请稍后重试！');resetUpgradeButton()})}function resetUpgradeButton(){var upgradeBtn=document.getElementById('upgradeBtn');var btnText=upgradeBtn.querySelector('.btn-text');upgradeBtn.classList.remove('loading');btnText.textContent='立即升级';upgradeBtn.disabled=false}



function openCompareModal(){document.getElementById('compareModal').style.display='block';document.body.style.overflow='hidden'}
function closeCompareModal(){document.getElementById('compareModal').style.display='none';document.body.style.overflow='auto'}
window.onclick=function(event){var modal=document.getElementById('compareModal');if(event.target==modal){closeCompareModal()}}
document.addEventListener('keydown',function(event){if(event.key==='Escape'){closeCompareModal()}});
    </script>
</asp:Content>

﻿<%@ Page Language="C#" AutoEventWireup="true" %>

<%@ Import Namespace="CommonLib" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5.0, shrink-to-fit=no">
    <title>VIP会员权益<%=PageTitleConst.Default_Ext_Short %></title>
    <meta name="description" content="OCR文字识别助手VIP会员权益详细介绍，包含个人版、专业版、旗舰版等VIP等级，详细的功能对比及价格方案">
    <link rel="stylesheet" href="./CSS/bootstrap.min.css?t=2023091301">
    <style type="text/css">
        html, table {
            font-size: 15px;
        }

        * {
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
            user-select: text !important;
        }

        th {
            border: 1px solid #dee2e6;
            text-align: center;
            font-weight: bold;
        }

        td {
            border: 1px solid #dee2e6;
        }

        .v-1 {
            background: linear-gradient(89.95deg, #6666FF 11.5%, #38c0ff 100.01%);
            color: white;
            background-color: #6666FF;
        }

        .v0 {
            color: white;
            background-color: rgb(23, 198, 83);
        }

        .v1 {
            background: linear-gradient(to right, #4B4B4B 5.77%, #1A1510 100%);
            color: #F9D9A8;
            background-color: #4B4B4B;
        }

        .v3 {
            background: linear-gradient(to right, #FFEBC1 21.65%, #FFE5B7 79.13%);
            color: #944800;
            border-top-right-radius: 0.05rem;
            background-color: #944800;
        }

        .column-highlight {
            position: absolute;
            border-radius: 5px;
            box-shadow: 0 0 10px 8px rgba(0, 0, 255, 0.5);
            -ms-box-shadow: 0 0 10px 8px rgba(0, 0, 255, 0.5);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease, left 0.2s ease;
            background-color: transparent;
            z-index: 10;
        }
    </style>
</head>
<body>
    <header>
        <h1 style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">VIP会员权益详情</h1>
    </header>
    <main>
    <div class="card-body">
        <table class="table table-striped" id="tbMain" aria-label="VIP会员特权对比表">
            <caption style="position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;">会员版本功能比较</caption>
            <thead>
                <tr>
                    <th style="min-width: 90px" scope="col">权益</th>
                    <%
                        var lstUserTypes = UserTypeHelper.GetCanRegUserTypes();
                        lstUserTypes.Insert(0, UserTypeHelper.GetUserType(UserTypeEnum.体验版));
                        foreach (var item in lstUserTypes)
                        {%>
                    <th class="v<%=item.Type.GetHashCode()%>" scope="col"><%=item.Type.ToString().Replace("体验","免费") %>
                    </th>
                    <%
                        } %>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>区别</td>
                    <%if (lstUserTypes.Any(p => p.Type == UserTypeEnum.体验版))
                        {  %>
                    <td>基础功能，永久免费</td>
                    <%} %>
                    <td>个人专属，轻松识别</td>
                    <td>专业高效，精确稳定</td>
                    <td>旗舰体验，最佳选择</td>
                </tr>
                <tr id="toUpdate" style="display: none;">
                    <td></td>
                    <%
                        for (int i = 0; i < lstUserTypes.Count; i++)
                        {
                            var item = lstUserTypes[i];
                            var url = item.Type.GetHashCode() == 0 ? Account.Web.CommonRequest.GetDownLoadUrl(Request) : "MemberUpgrade.aspx?type=" + item.Type.GetHashCode();
                    %>
                    <td>
                        <a class="v<%=item.Type.GetHashCode()%>" style="display: inline-block; width: 100%; height: 40px; border-radius: 20px; font-size: 18px; font-weight: 500; text-align: center; line-height: 40px; cursor: pointer;"
                            href="<%=url %>"><%=item.Type.GetHashCode() == 0?"免费下载":"升级到"+item.Type.ToString() %></a>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>截图</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>贴图</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>截图识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>✔</td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>离线识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportLocalOcr)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>图片识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportImageFile)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>区域识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportVertical)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>竖排识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportVertical)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>划词翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportTranslate)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>图片翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportTranslate)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>公式识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportMath)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>表格识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportTable)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>文档识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportDocFile)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>文档翻译</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportDocFile)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>批量识别</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportBatch)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>自选通道</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportPassage)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>多结果</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSetOtherResult)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>多设备</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=item.MaxLoginCount>1?item.MaxLoginCount.ToString():"✘" %></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>识别频率</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=(item.PerTimeSpan>0? (item.PerTimeSpan / 1000).ToString("F0")+"秒/"+item.PerTimeSpanExecCount+"次":"-").Replace("/1次","/次") %></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td><b>每日限额</b></td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td><b><%=item.LimitPerDayCount+"次"%></b></td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>专属客服</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.MaxLoginCount > 1)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        } %>
                </tr>
                <tr>
                    <td>需求定制</td>
                    <%foreach (var item in lstUserTypes)
                        {%>
                    <td>
                        <%if (item.IsSupportTranslate)
                            {
                        %>
                        ✔
                        <%}
                            else
                            { %>
                        ✘
                        <%}%>
                    </td>
                    <%
                        }
                    %>
                </tr>
            </tbody>
        </table>
    </div>
    </main>
    <script type="text/javascript">
        // 页面加载好后运行初始化
        (window.addEventListener ? window.addEventListener('DOMContentLoaded', init, false) : 
         window.attachEvent ? window.attachEvent('onload', init) : window.onload = init);
         
        function init() {
            var urlParams = location.search.substr(1).split('&').reduce(function(o,p){
                var pair = p.split('=');
                if (pair[0]) {
                    try {
                        o[pair[0]] = decodeURIComponent(pair[1] || '');
                    } catch(e) {
                        o[pair[0]] = pair[1] || '';
                    }
                }
                return o;
            }, {});
            
            // 设置更新按钮显示状态
            var toUpdateEl = document.getElementById("toUpdate");
            if (toUpdateEl) toUpdateEl.style.display = self != top && !urlParams.frame ? "" : "none";
            
            // 隐藏第一列(如果需要)
            if (!urlParams.shownormal || parseInt(urlParams.shownormal) === 0) {
                hideColumn(1);
            }
            
            // 初始化高亮列
            var colIndex = 2; // 默认高亮第二列
            
            // 根据类型参数高亮相应列
            var type = urlParams.type ? parseInt(urlParams.type) : -2;
            if (type > -2) {
                var ths = document.getElementById('tbMain').getElementsByTagName('th');
                for (var i = 0; i < ths.length; i++) {
                    if (ths[i].className.indexOf('v' + type) !== -1 && ths[i].style.display != 'none') {
                        colIndex = i;
                        break;
                    }
                }
            }
            
            // 初始化列高亮
            highlightCol(colIndex);
            
            // 为所有单元格添加鼠标事件
            var cells = document.getElementById('tbMain').getElementsByTagName("td");
            var heads = document.getElementById('tbMain').getElementsByTagName("th");
            addHoverEvents(cells);
            addHoverEvents(heads);
        }
        
        // 隐藏指定列
        function hideColumn(colIdx) {
            var table = document.getElementById('tbMain');
            var th = table.rows[0].cells[colIdx];
            if (th) th.style.display = 'none';
            
            for (var i = 0; i < table.rows.length; i++) {
                var cell = table.rows[i].cells[colIdx];
                if (cell) cell.style.display = 'none';
            }
        }
        
        // 为单元格添加鼠标事件
        function addHoverEvents(cells) {
            for (var i = 0; i < cells.length; i++) {
                if (window.addEventListener) {
                    cells[i].addEventListener('mouseenter', hoverHandler, false);
                } else if (cells[i].attachEvent) {
                    cells[i].attachEvent('onmouseenter', function() {
                        var idx = window.event.srcElement.cellIndex;
                        if (idx > 0) highlightCol(idx);
                    });
                }
            }
        }
        
        // 处理鼠标悬停
        function hoverHandler() {
            var idx = this.cellIndex;
            if (idx > 0) highlightCol(idx);
        }
        
        // 高亮列
        function highlightCol(idx) {
            var hlDiv = document.getElementById('col-highlight') || createHighlightDiv();
            var table = document.getElementById('tbMain');
            var cell = table.rows[0].cells[idx];
            
            // 设置元素尺寸和位置
            var tRect = table.getBoundingClientRect();
            var cRect = cell.getBoundingClientRect();
            
            hlDiv.style.left = (cRect.left - tRect.left + 5) + 'px';
            hlDiv.style.top = '5px';
            hlDiv.style.width = (cell.offsetWidth - 10) + 'px';
            hlDiv.style.height = (table.offsetHeight - 10) + 'px';
            
            // 设置高亮效果
            var bgColor = cell.currentStyle ? cell.currentStyle.backgroundColor : 
                         getComputedStyle(cell, null).backgroundColor;
            hlDiv.style.boxShadow = '0 0 10px 8px ' + bgColor;
            hlDiv.style.opacity = '1';
        }
        
        // 创建高亮元素
        function createHighlightDiv() {
            var div = document.createElement('div');
            div.id = 'col-highlight';
            div.className = 'column-highlight';
            document.body.appendChild(div);
            return div;
        }
    </script>
    <div id="translate" class="ignore" style="display: none;"></div>
    <script src="/static/js/translate.js"></script>
</body>
</html>
